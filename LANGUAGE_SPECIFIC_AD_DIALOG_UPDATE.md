# تحديث المربع المنبثق للإعلان حسب اللغة المحددة
# Language-Specific Ad Dialog Update

## 🎯 الهدف من التحديث - Update Objective

تم تحديث المربع المنبثق لطلب مشاهدة الإعلان ليظهر بلغة واحدة فقط حسب اللغة التي اختارها المستخدم (عربي أو إنجليزي) بدلاً من عرض النص بكلا اللغتين.

## ✅ التحديثات المنجزة - Completed Updates

### 1. تحديث ملف strings.xml
- ✅ إضافة نصوص منفصلة للغة العربية (`_ar`)
- ✅ إضافة نصوص منفصلة للغة الإنجليزية (`_en`)
- ✅ إزالة النصوص ثنائية اللغة

#### النصوص الجديدة:
```xml
<!-- Arabic Strings -->
<string name="ad_dialog_title_ar">تحميل المود</string>
<string name="ad_dialog_message_ar">لمشاهدة إعلان واحد لتحميل هذا المود، يرجى الضغط على موافق.</string>
<string name="ad_dialog_checkbox_ar">موافق دائماً (عدم الإظهار مرة أخرى)</string>
<string name="ad_dialog_ok_ar">موافق</string>
<string name="ad_dialog_cancel_ar">إلغاء</string>

<!-- English Strings -->
<string name="ad_dialog_title_en">Download Mod</string>
<string name="ad_dialog_message_en">To download this mod, please watch one ad.</string>
<string name="ad_dialog_checkbox_en">Always agree (Don't show again)</string>
<string name="ad_dialog_ok_en">OK</string>
<string name="ad_dialog_cancel_en">Cancel</string>
```

### 2. تحديث MainActivity.kt
- ✅ إضافة منطق للحصول على لغة المستخدم من `localStorage.getItem('selectedLanguage')`
- ✅ تحديث دالة `showAdOrDownload()` لاستخدام النصوص المناسبة حسب اللغة
- ✅ إضافة دالة مساعدة `getUserLanguage()` في WebAppInterface

#### المنطق الجديد:
```kotlin
// Get user language from JavaScript
webView.evaluateJavascript("localStorage.getItem('selectedLanguage') || 'en'") { result ->
    val userLanguage = result?.replace("\"", "") ?: "en"
    val isArabic = userLanguage == "ar"
    
    // Show dialog with appropriate language
    if (isArabic) {
        builder.setTitle(getString(R.string.ad_dialog_title_ar))
        builder.setMessage(getString(R.string.ad_dialog_message_ar))
    } else {
        builder.setTitle(getString(R.string.ad_dialog_title_en))
        builder.setMessage(getString(R.string.ad_dialog_message_en))
    }
}
```

## 🔄 آلية العمل الجديدة - New Workflow

### 1. تحديد اللغة:
- يتم الحصول على لغة المستخدم من `localStorage.getItem('selectedLanguage')`
- إذا لم تكن محددة، يتم استخدام الإنجليزية كافتراضي
- يتم تسجيل اللغة المكتشفة في الـ logs

### 2. عرض المربع المنبثق:
- **إذا كانت اللغة عربية (`ar`)**:
  - العنوان: "تحميل المود"
  - الرسالة: "لمشاهدة إعلان واحد لتحميل هذا المود، يرجى الضغط على موافق."
  - Checkbox: "موافق دائماً (عدم الإظهار مرة أخرى)"
  - الأزرار: "موافق" و "إلغاء"

- **إذا كانت اللغة إنجليزية (`en`)**:
  - العنوان: "Download Mod"
  - الرسالة: "To download this mod, please watch one ad."
  - Checkbox: "Always agree (Don't show again)"
  - الأزرار: "OK" و "Cancel"

### 3. حفظ التفضيلات:
- يتم حفظ اختيار "موافق دائماً" بنفس الطريقة السابقة
- لا يتأثر بتغيير اللغة لاحقاً

## 🧪 اختبار الميزة الجديدة - Testing the New Feature

### سيناريوهات الاختبار:

1. **مستخدم باللغة العربية**:
   - تأكد من أن `localStorage.getItem('selectedLanguage')` يعيد `'ar'`
   - اضغط على زر تحميل مود
   - يجب أن يظهر المربع باللغة العربية فقط

2. **مستخدم باللغة الإنجليزية**:
   - تأكد من أن `localStorage.getItem('selectedLanguage')` يعيد `'en'`
   - اضغط على زر تحميل مود
   - يجب أن يظهر المربع باللغة الإنجليزية فقط

3. **مستخدم بدون لغة محددة**:
   - امسح `localStorage.getItem('selectedLanguage')`
   - اضغط على زر تحميل مود
   - يجب أن يظهر المربع باللغة الإنجليزية (افتراضي)

4. **تغيير اللغة**:
   - غير اللغة من العربية للإنجليزية أو العكس
   - اضغط على زر تحميل مود
   - يجب أن يظهر المربع باللغة الجديدة

### أوامر الاختبار في Console:
```javascript
// فحص اللغة الحالية
console.log('Current language:', localStorage.getItem('selectedLanguage'));

// تغيير اللغة للعربية
localStorage.setItem('selectedLanguage', 'ar');

// تغيير اللغة للإنجليزية
localStorage.setItem('selectedLanguage', 'en');

// مسح اللغة (للاختبار الافتراضي)
localStorage.removeItem('selectedLanguage');
```

## 📝 ملاحظات مهمة - Important Notes

1. **التوافق مع النظام الحالي**: 
   - جميع الوظائف الأخرى تعمل بنفس الطريقة
   - لا تأثير على نظام الإعلانات أو التحميل

2. **الأداء**:
   - يتم استدعاء JavaScript مرة واحدة فقط لتحديد اللغة
   - لا يؤثر على سرعة عرض المربع

3. **معالجة الأخطاء**:
   - في حالة فشل الحصول على اللغة، يتم استخدام الإنجليزية
   - يتم تسجيل جميع الأخطاء في الـ logs

4. **المرونة**:
   - يمكن إضافة لغات جديدة بسهولة
   - يمكن تخصيص النصوص لكل لغة بشكل منفصل

## 🎯 النتائج المتوقعة - Expected Results

- ✅ المربع يظهر بلغة واحدة فقط حسب اختيار المستخدم
- ✅ النصوص واضحة ومناسبة لكل لغة
- ✅ تجربة مستخدم محسنة ومخصصة
- ✅ عدم وجود ازدواجية في النصوص
- ✅ سهولة القراءة والفهم

---

**تم تنفيذ التحديث بنجاح! المربع المنبثق الآن يظهر بلغة المستخدم المحددة فقط. ✅**
