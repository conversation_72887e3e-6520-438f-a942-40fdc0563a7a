<!DOCTYPE html>
<html dir="ltr" lang="en">
<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mod Etaris</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="image-display-styles.css">

<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<script>
    // Check if language is selected, if not redirect to language selection
    function checkLanguageSelection() {
        // Check URL parameters for language selection
        const urlParams = new URLSearchParams(window.location.search);
        const langParam = urlParams.get('lang');
        
        // If language parameter is in URL, save it to localStorage
        if (langParam) {
            try {
                localStorage.setItem('languageSelected', 'true');
                localStorage.setItem('selectedLanguage', langParam);
                console.log('Language set from URL parameter:', langParam);
                // Clean URL by removing the parameter
                if (history.replaceState) {
                    const newUrl = window.location.pathname;
                    history.replaceState(null, '', newUrl);
                }
                return true;
            } catch (error) {
                console.error('Error saving language from URL parameter:', error);
            }
        }
        
        // Check cookies as fallback
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
        
        const cookieLanguageSelected = getCookie('languageSelected');
        if (cookieLanguageSelected === 'true') {
            try {
                localStorage.setItem('languageSelected', 'true');
                const selectedLang = getCookie('selectedLanguage') || 'en';
                localStorage.setItem('selectedLanguage', selectedLang);
                console.log('Language set from cookies:', selectedLang);
                return true;
            } catch (error) {
                console.error('Error saving language from cookies:', error);
            }
        }
        
        // Check if we're coming from language selection page to prevent redirect loops
        const referrer = document.referrer;
        const isFromLanguageSelection = referrer && referrer.includes('language-selection.html');
        
        // Check sessionStorage as fallback
        const sessionLanguageSelected = sessionStorage.getItem('languageSelected');
        if (sessionLanguageSelected === 'true') {
            try {
                localStorage.setItem('languageSelected', 'true');
                const selectedLang = sessionStorage.getItem('selectedLanguage') || 'en';
                localStorage.setItem('selectedLanguage', selectedLang);
                console.log('Language set from sessionStorage:', selectedLang);
                return true;
            } catch (error) {
                console.error('Error saving language from sessionStorage:', error);
            }
        }
        
        // Get language selection status from localStorage
        const languageSelected = localStorage.getItem('languageSelected');
        
        // If language is not selected and we're not coming from language selection page
        if (languageSelected !== 'true' && !isFromLanguageSelection) {
            // Check if we're in a potential redirect loop
            const redirectCount = parseInt(sessionStorage.getItem('languageRedirectCount') || '0');
            if (redirectCount > 2) {
                console.warn('Detected potential redirect loop, forcing language selection to English');
                try {
                    localStorage.setItem('languageSelected', 'true');
                    localStorage.setItem('selectedLanguage', 'en');
                    sessionStorage.removeItem('languageRedirectCount');
                    return true;
                } catch (error) {
                    console.error('Error forcing language selection:', error);
                }
            }
            
            // Increment redirect counter
            sessionStorage.setItem('languageRedirectCount', (redirectCount + 1).toString());
            
            // Set a temporary flag to prevent redirect loops
            sessionStorage.setItem('redirectingToLanguageSelection', 'true');
            window.location.href = 'language-selection.html';
            return false;
        }
        
        // If we're here, either language is selected or we're in a potential loop
        // Clear any temporary redirect flags
        sessionStorage.removeItem('redirectingToLanguageSelection');
        sessionStorage.removeItem('languageRedirectCount');
        return true;
    }

    // Check immediately when script loads
    if (!checkLanguageSelection()) {
        // Stop further execution if redirecting
        document.addEventListener('DOMContentLoaded', function(e) {
            e.stopImmediatePropagation();
        });
    }
</script>
</head>
<body>
    <div class="top-fixed-bar">
        <!-- Buttons moved out -->
    </div>
    <div class="header">
        <button class="drawer-btn" onclick="if(typeof toggleDrawer === 'function') toggleDrawer(); else console.error('toggleDrawer not loaded yet');">
            <img src="image/menu.png" alt="Menu" class="button-icon">
        </button>
        <button class="search-btn" onclick="navigateToSearchPage()">
            <img src="image/search.png" alt="Search" class="button-icon">
        </button>
        <img src="image/app_name.png" alt="App Name" class="app-logo-header"> <!-- Changed H1 to IMG -->
        <div class="categories-scroll-container">
            <div id="categories">
                <button class="category-btn" data-category="All">All</button>
                <button class="category-btn" data-category="Addons">Addons</button>
                <button class="category-btn" data-category="Shaders">Shaders</button>
                <button class="category-btn" data-category="Texture">Texture Pack</button>
                <button class="category-btn" data-category="Maps">Maps</button>
                <button class="category-btn" data-category="Seeds">Seeds</button>
                <button class="category-btn" data-category="News">News</button>
                <!-- Search and Drawer buttons moved to top-fixed-bar -->
            </div>
        </div>
    </div>

    <div class="drawer" id="drawer">
        <!-- Links will be loaded dynamically here by script.js -->
    </div>
    <div id="overlay" class="drawer-overlay"></div>
    <div class="modal" id="modal"> <!-- Reverted ID -->
        <div class="modal-content">
            <!-- Close button might be added dynamically by JS now, or keep static -->
            <span class="close-btn" onclick="closeModal()"><img src="image/close_icon.png" alt="Close" style="width: 20px; height: 20px; vertical-align: middle;"></span>
            <div id="modalContent">
                <!-- Content will be injected here by script.js -->
            </div>
        </div>
        <!-- Download bar might be part of injected content or needs separate handling -->
    </div>

<div id="main-content-wrapper" class="initial-hidden-animate">
<!-- Sorting Buttons -->
<div id="sortButtons" class="sort-buttons"> <!-- Removed inline style -->
    <button class="sort-btn" onclick="sortMods('likes')">Most Liked</button>
    <button class="sort-btn" onclick="sortMods('newest')">Newest</button>
    <button class="sort-btn" onclick="sortMods('downloads')">Most Downloads</button>
    <button class="sort-btn" onclick="sortMods('oldest')">Oldest</button>
</div>

<!-- Container for Single Category View -->
<div id="singleCategoryContainer" style="padding: 0 10px;"> <!-- Removed inline style -->
    <div id="modsGrid" class="mods-grid">
        <!-- Mods for a single selected category will be loaded here by script.js -->
    </div>
</div>

<!-- Sections for "All" Category View -->
<!-- News - First section -->
<div class="category-section" id="news-section">
    <h2 class="category-header">
        News <img src="image/Time-icon.png" alt="News" class="category-icon" style="width: 24px; height: 24px; top: 10px;">
        <span class="see-all-btn" onclick="filterItems('News')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="news-mods">
        <!-- News mods will be loaded here -->
    </div>
</div>

<!-- Addons - Second section (includes Free Addons) -->
<div class="category-section" id="addons-section">
    <h2 class="category-header">
        Addons <img src="image/icon_Addons.png" alt="Addons" class="category-icon">
        <span class="see-all-btn" onclick="filterItems('Addons')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="addons-mods">
        <!-- Addons mods will be loaded here -->
    </div>
</div>

<!-- Gift - Third section -->
<div class="category-section" id="suggested-mods-section">
    <h2 class="category-header">
        Gift <img src="image/suggested_icon.png" alt="Gift" class="category-icon" style="width: 40px; height: 40px; top: 2px; margin-left: 5px;">
        <span class="see-all-btn" onclick="filterItems('Gift')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="suggested-mods">
        <!-- Suggested mods will be loaded here -->
    </div>
</div>

<!-- Shaders - Third section -->
<div class="category-section" id="shaders-section">
    <h2 class="category-header">
        Shaders <img src="image/icon_shaders.png" alt="Shaders" class="category-icon">
        <span class="see-all-btn" onclick="filterItems('Shaders')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="shaders-mods">
        <!-- Shaders mods will be loaded here -->
    </div>
</div>

<!-- Texture Pack - Fourth section -->
<div class="category-section" id="texture-pack-section">
    <h2 class="category-header">
        Texture Pack <img src="image/texter.png" alt="Texture Pack" class="category-icon">
        <span class="see-all-btn" onclick="filterItems('Texture')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="texture-pack-mods">
        <!-- Texture Pack mods will be loaded here -->
    </div>
</div>

<!-- Seeds - Fifth section -->
<div class="category-section" id="seeds-section">
    <h2 class="category-header">
        Seeds <img src="image/seeds-icon.png" alt="Seeds" class="category-icon">
        <span class="see-all-btn" onclick="filterItems('Seeds')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="seeds-mods">
        <!-- Seeds mods will be loaded here -->
    </div>
</div>

<!-- Maps - Sixth section -->
<div class="category-section" id="maps-section">
    <h2 class="category-header">
        Maps <img src="image/maps-icon.png" alt="Maps" class="category-icon">
        <span class="see-all-btn" onclick="filterItems('Maps')">see all <img src="image/downarrow.png" alt="arrow"></span>
    </h2>
    <div class="items-container" id="maps-mods">
        <!-- Maps mods will be loaded here -->
    </div>
</div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- إدارة Supabase البسيطة -->
    <script src="supabase-manager.js"></script>
    <!-- الملفات الأساسية فقط -->
    <script src="translations.js"></script>
    <script src="network-handler.js"></script>
    <script src="social-icons-manager.js"></script>
    <script src="custom-sections-manager.js"></script>
    <script src="backup-ads-integration.js"></script>

    <!-- الملف الرئيسي -->
    <script src="script.js"></script>

    <!-- Emergency drawer toggle function -->
    <script>
        // Backup drawer toggle function in case main script hasn't loaded yet
        if (typeof toggleDrawer === 'undefined') {
            function toggleDrawer() {
                console.log("Emergency toggleDrawer() called");
                const drawer = document.querySelector(".drawer");
                const overlay = document.querySelector(".drawer-overlay");
                if (drawer) drawer.classList.add("active");
                if (overlay) overlay.classList.add("active");
            }
        }
    </script>

    <!-- Removed Bottom Fixed Bar -->
</div>

<!-- Floating Subscription Icon -->
<div id="floatingSubscriptionIcon" class="floating-icon" style="display: none;">
    <img id="floatingIconImage" src="" alt="Free Subscription" class="floating-icon-image">
</div>

</body>
</html>