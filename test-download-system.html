<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التحميل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>اختبار نظام التحميل - تطبيق مودات ماين كرافت</h1>
    
    <div class="test-container">
        <h2>اختبار الإعلانات المكافئة والتحميل</h2>
        <p>هذا الاختبار يحاكي عملية التحميل الطبيعية مع الإعلانات المكافئة</p>
        
        <button class="test-button" onclick="testDownloadFlow()">
            اختبار تدفق التحميل الكامل
        </button>
        
        <button class="test-button" onclick="testOpenDownloadedMod()">
            اختبار فتح مود محمل مسبقاً
        </button>
        
        <button class="test-button" onclick="testFallbackSystem()">
            اختبار النظام الاحتياطي
        </button>
        
        <button class="test-button" onclick="clearLogs()">
            مسح السجلات
        </button>
    </div>
    
    <div class="test-container">
        <h3>حالة النظام</h3>
        <div id="systemStatus"></div>
    </div>
    
    <div class="test-container">
        <h3>سجل الاختبارات</h3>
        <div id="logArea" class="log-area"></div>
    </div>

    <script>
        // محاكاة AndroidInterface للاختبار
        if (typeof AndroidInterface === 'undefined') {
            window.AndroidInterface = {
                requestModDownloadWithAd: function(modId, modName, downloadUrl) {
                    log(`🎯 AndroidInterface.requestModDownloadWithAd called: ${modId}, ${modName}, ${downloadUrl}`, 'success');
                    
                    // محاكاة عرض الإعلان
                    setTimeout(() => {
                        log('📺 عرض الإعلان المكافئة...', 'warning');
                        
                        // محاكاة إغلاق الإعلان وبدء التحميل
                        setTimeout(() => {
                            log('✅ تم إغلاق الإعلان، بدء التحميل...', 'success');
                            
                            // محاكاة اكتمال التحميل
                            setTimeout(() => {
                                log('📁 اكتمل التحميل، فتح الملف...', 'success');
                                localStorage.setItem(`downloaded_${modId}`, 'true');
                                updateSystemStatus();
                            }, 2000);
                        }, 3000);
                    }, 1000);
                },
                
                openDownloadedMod: function(modId, modName, downloadUrl) {
                    log(`📂 AndroidInterface.openDownloadedMod called: ${modId}, ${modName}`, 'success');
                    
                    // محاكاة فتح الملف
                    setTimeout(() => {
                        log('🎮 تم فتح الملف في ماين كرافت!', 'success');
                    }, 1000);
                }
            };
        }

        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logArea').textContent = '';
        }

        function updateSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            
            let status = '<div class="status success">✅ AndroidInterface متوفر</div>';
            
            if (typeof handleDownload === 'function') {
                status += '<div class="status success">✅ دالة handleDownload متوفرة</div>';
            } else {
                status += '<div class="status error">❌ دالة handleDownload غير متوفرة</div>';
            }
            
            if (typeof downloadModFileWithFallback === 'function') {
                status += '<div class="status success">✅ نظام التحميل الاحتياطي متوفر</div>';
            } else {
                status += '<div class="status warning">⚠️ نظام التحميل الاحتياطي غير متوفر</div>';
            }
            
            const downloadedMods = Object.keys(localStorage).filter(key => key.startsWith('downloaded_'));
            status += `<div class="status success">📁 عدد المودات المحملة: ${downloadedMods.length}</div>`;
            
            statusDiv.innerHTML = status;
        }

        function testDownloadFlow() {
            log('🔄 بدء اختبار تدفق التحميل الكامل...', 'warning');
            
            const testModId = 'test_mod_' + Date.now();
            const testModName = 'Test Mod';
            const testDownloadUrl = 'https://example.com/test_mod.mcpack';
            
            log(`📋 معلومات الاختبار: ID=${testModId}, Name=${testModName}`, 'info');
            
            // محاكاة النقر على زر التحميل
            if (typeof handleDownload === 'function') {
                handleDownload(testModId, testModName, testDownloadUrl);
            } else {
                // استدعاء مباشر للـ AndroidInterface
                AndroidInterface.requestModDownloadWithAd(testModId, testModName, testDownloadUrl);
            }
        }

        function testOpenDownloadedMod() {
            log('📂 بدء اختبار فتح مود محمل مسبقاً...', 'warning');
            
            const testModId = 'downloaded_mod_test';
            const testModName = 'Downloaded Test Mod';
            const testDownloadUrl = 'https://example.com/downloaded_mod.mcpack';
            
            // تعيين المود كمحمل مسبقاً
            localStorage.setItem(`downloaded_${testModId}`, 'true');
            log(`✅ تم تعيين المود كمحمل مسبقاً: ${testModId}`, 'success');
            
            // محاولة فتح المود
            if (typeof handleDownload === 'function') {
                handleDownload(testModId, testModName, testDownloadUrl);
            } else {
                AndroidInterface.openDownloadedMod(testModId, testModName, testDownloadUrl);
            }
        }

        function testFallbackSystem() {
            log('🛡️ بدء اختبار النظام الاحتياطي...', 'warning');
            
            if (typeof downloadModFileWithFallback === 'function') {
                const testModId = 'fallback_test_' + Date.now();
                const testModName = 'Fallback Test Mod';
                const testDownloadUrl = 'https://example.com/fallback_test.mcpack';
                
                downloadModFileWithFallback(testModId, testModName, testDownloadUrl);
            } else {
                log('❌ النظام الاحتياطي غير متوفر', 'error');
            }
        }

        // تحديث حالة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStatus();
            log('🚀 تم تحميل صفحة الاختبار', 'success');
        });
    </script>
</body>
</html>
