# دليل سريع لحل مشكلة البناء - Quick Build Fix Guide

## المشكلة - Problem
```
ERROR: JAVA_HOME is set to an invalid directory
```

## الحلول المتاحة - Available Solutions

### الحل 1: استخدام Android Studio
1. افتح المشروع في Android Studio
2. انتظر حتى يكتمل التزامن (Sync)
3. اذهب إلى Build → Build Bundle(s) / APK(s) → Build APK(s)
4. انتظر حتى يكتمل البناء

### الحل 2: إصلاح JAVA_HOME يدوياً
```batch
# في Command Prompt
set JAVA_HOME=C:\Program Files\Java\jdk-24
set PATH=%JAVA_HOME%\bin;%PATH%
gradlew assembleDebug
```

### الحل 3: استخدام ملف البناء المبسط
```batch
# تشغيل ملف البناء
.\build-simple.bat
```

### الحل 4: تحديث متغيرات النظام
1. اذهب إلى Control Panel → System → Advanced System Settings
2. انقر على Environment Variables
3. في System Variables، ابحث عن JAVA_HOME
4. إذا كان موجوداً، عدّله إلى: `C:\Program Files\Java\jdk-24`
5. إذا لم يكن موجوداً، أنشئه بالقيمة: `C:\Program Files\Java\jdk-24`
6. أعد تشغيل Command Prompt

## التحقق من Java
```batch
# التحقق من إصدار Java
java -version

# التحقق من JAVA_HOME
echo %JAVA_HOME%
```

## بناء التطبيق بعد الإصلاح
```batch
# تنظيف المشروع
gradlew clean

# بناء APK للاختبار
gradlew assembleDebug

# بناء APK للإصدار النهائي
gradlew assembleRelease
```

## موقع ملف APK بعد البناء
```
app\build\outputs\apk\debug\app-debug.apk
app\build\outputs\apk\release\app-release.apk
```

## اختبار التطبيق
1. انسخ ملف APK إلى هاتفك
2. قم بتثبيته
3. اختبر تحميل مود:
   - يجب أن يظهر الإعلان أولاً
   - ثم يبدأ التحميل
   - ثم يفتح الملف تلقائياً

## إذا استمرت المشكلة
1. تأكد من تثبيت Java JDK (ليس JRE فقط)
2. تأكد من أن مسار Java صحيح
3. جرب إعادة تشغيل الكمبيوتر
4. استخدم Android Studio بدلاً من سطر الأوامر

## ملاحظات مهمة
- تم إصلاح نظام التحميل في الكود
- المشكلة الحالية فقط في البناء
- النظام سيعمل بشكل صحيح بعد البناء الناجح
