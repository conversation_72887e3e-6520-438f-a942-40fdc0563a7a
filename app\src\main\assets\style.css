/* تحسين تحميل الخطوط مع fallback محسن */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');


/* Language Selection Styles */
.language-selection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw; /* Use viewport width */
    height: 100vh; /* Use viewport height */
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    display: block; /* Changed from flex to block */
    z-index: 100010;
    animation: fadeIn 0.5s ease;
    padding: 0; /* Remove padding from overlay */
    margin: 0; /* Remove margin */
    overflow: hidden; /* Prevent any overflow */
    box-sizing: border-box; /* Include padding in width calculation */
}

.language-selection-container {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 0; /* Ensure no border-radius */
    padding: 0; /* Remove all padding */
    margin: 0; /* Remove all margin */
    text-align: center;
    border: none; /* Ensure no border */
    box-shadow: none;
    max-width: 100vw; /* Use viewport width */
    width: 100vw; /* Use viewport width */
    height: 100vh; /* Use viewport height */
    animation: slideInUp 0.6s ease;
    position: absolute; /* Position absolutely */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto; /* Allow vertical scrolling if needed */
    box-sizing: border-box; /* Include padding in width calculation */
}

.language-selection-title {
    font-size: 2rem;
    color: #ffcc00;
    margin: 50px auto 10px auto; /* Add top margin and center horizontally */
    font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
    /* text-shadow removed */
    width: 90%; /* Take up most of the container width */
    max-width: 90%; /* Increased from 500px */
    padding: 0 10px; /* Add horizontal padding */
    box-sizing: border-box; /* Include padding in width calculation */
}

.language-selection-subtitle {
    font-size: 1.2rem;
    color: #ffffff;
    margin: 0 auto 30px auto; /* Center horizontally with bottom margin */
    opacity: 0.9;
    width: 90%; /* Take up most of the container width */
    max-width: 90%; /* Increased from 500px */
    padding: 0 10px; /* Add horizontal padding */
    box-sizing: border-box; /* Include padding in width calculation */
}

.language-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 0 auto 20px auto; /* Center horizontally with bottom margin */
    width: 90%; /* Take up most of the container width */
    max-width: 90%; /* Increased from 500px */
    padding: 0 10px; /* Add horizontal padding */
    box-sizing: border-box; /* Include padding in width calculation */
}

.language-option {
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    color: #000;
    border: none;
    padding: 15px 30px;
    border-radius: 15px;
    font-size: 1.3rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: none;
    font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
}

.language-option:hover {
    transform: translateY(-3px);
    box-shadow: none;
    background: linear-gradient(45deg, #ffd700, #ffb300);
}

.language-option:active {
    transform: translateY(-1px);
}

.language-flag {
    font-size: 2rem;
    margin-right: 10px;
    vertical-align: middle;
}

.language-close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background: transparent;
    border: none;
    color: #ffcc00;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.language-close-button:hover {
    background-color: rgba(255, 204, 0, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional animations for language modal */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInScaleUp {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Mobile-specific adjustments for language selection */
@media (max-width: 768px) {
    html, body {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }
    
    .language-selection-overlay {
        width: 100vw !important;
        max-width: 100vw !important;
        left: 0 !important;
        right: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        border-radius: 0 !important;
    }
    
    .language-selection-container {
        width: 100vw !important;
        max-width: 100vw !important;
        left: 0 !important;
        right: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        border-radius: 0 !important;
    }
    
    .language-options {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 10px !important;
        box-sizing: border-box !important;
    }
    
    .language-option {
        width: 100% !important;
        margin: 0 auto !important;
        box-sizing: border-box !important;
    }
    
    .language-selection-title,
    .language-selection-subtitle {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 10px !important;
        box-sizing: border-box !important;
    }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --secondary-color: #ffffff;
    --background-color: #21221f;
    --card-background: #000000;
    --text-color: #ffffff;
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --primary-color: transparent;
    --accent-color: #ffcc00;
}

html {
    background-color: #21221f; /* Match body background */
    width: 100%; /* Ensure full width */
    overflow-x: hidden; /* Also prevent overflow here */
    border: none; /* Explicitly remove border */
    box-shadow: none; /* Explicitly remove shadow */
    outline: none; /* Explicitly remove outline */
}

/* --- New Top Fixed Bar --- */
.top-fixed-bar {
    position: fixed !important;
    top: 0 !important; /* Reverted to original position */
    left: 0 !important; /* Adjust to right: 0; if needed for RTL, but left: 0 usually works */
    width: 100% !important;
    height: 45px !important; /* Increased height */
    background: linear-gradient(to right, #ffcc00, #ff9800) !important; /* Match header gradient */
    color: white !important; /* Change text color to white for better contrast */
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important; /* Space out items: button, span, button */
    padding: 0 10px !important; /* Adjust padding slightly */
    z-index: 100004 !important; /* Increased z-index to be above install modal (100003) */
    box-shadow: none !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* .top-fixed-bar span { */ /* Removed the span style as the span element is removed */
    /* font-size: 0.9rem; */
    /* font-weight: bold; */
/* } */
/* --- End New Top Fixed Bar --- */

body {
    padding-top: 55px; /* Adjusted padding for 45px fixed bar */
    background-color: #21221f !important; /* Explicitly set dark background color and force override */
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden; /* Keep this */
    width: 100%; /* Ensure body takes full width of html */
    border: none; /* Explicitly remove border */
    box-shadow: none; /* Explicitly remove shadow */
    outline: none; /* Explicitly remove outline */
    /* منع تحديد النص */
    user-select: none; /* Standard syntax */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    padding-bottom: 60px; /* Reduced bottom padding to avoid excessive space */
}

/* Hide scrollbar for Chrome, Safari and Opera (including WebView) */
body::-webkit-scrollbar {
    display: none;
}

/* تحسينات الأداء للصور */
img {
    /* تحسين عرض الصور */
    image-rendering: optimizeQuality;
    /* منع تمدد الصور */
    object-fit: cover;
    /* تحسين الانتقالات */
    transition: opacity 0.3s ease, transform 0.3s ease;
    /* تحسين الأداء */
    transform: translateZ(0);
}

/* تحسين أداء الصور في البطاقات */
.item img, .mod-card img {
    /* ضغط الصور تلقائياً */
    image-rendering: optimizeSpeed;
    /* تحسين الذاكرة */
    will-change: transform;
    /* تحسين العرض */
    backface-visibility: hidden;
    /* تحسين الأداء */
    transform: translateZ(0);
}

/* تحسين الصور الكبيرة */
.modal img {
    /* تحسين جودة الصور في النوافذ المنبثقة */
    image-rendering: optimizeQuality;
    /* تحسين الأداء */
    transform: translateZ(0);
}

/* Prevent body and html scrolling when modal is open */
/* html.modal-open {   <-- commented out to restore scroll

    overflow: hidden;
    position: fixed;
    width: 100%;
}
.modal-open {
    overflow: hidden;
    /* position: relative; */ /* تم تعطيل هذه الخاصية لحل مشكلة عدم فتح النوافذ */


/* --- Original Header Styles (Restored for index.html) --- */
.header {
    position: relative;
    width: 100%;
    height: 100px;
    display: flex;
    flex-direction: row; /* Changed to row for horizontal layout */
    align-items: center; /* Vertically center items */
    justify-content: space-between; /* Space out items */
    background: linear-gradient(to right, #ffcc00, #ff9800);
    padding: 0 15px; /* Adjusted padding */
    box-shadow: none;
    margin-bottom: 30px;
    margin-top: -10px;
}

.header-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1; /* الصورة في الخلفية */
}

/* Removed .back-btn rule - will be added specifically for search page */

.header h1 {
    font-size: 2rem; /* Original size */
    font-weight: bold;
    color: white;
    margin-bottom: 10px; /* Original margin */
    /* Removed flex properties */
}

.app-logo-header {
    height: 60px;
    object-fit: contain;
    position: absolute; /* Position absolutely within the header */
    left: 50%;
    top: 5px; /* Adjust top position */
    transform: translateX(-50%); /* Center horizontally */
    z-index: 1; /* Ensure it's above the scroll container if needed */
}

/* Removed fadeInLogo keyframes as we are using fadeInUp now */
/* @keyframes fadeInLogo {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
} */


.categories-scroll-container {
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    width: calc(100% - 30px); /* Adjusted width to span almost full header width */
    position: absolute;
    bottom: 0; /* Position at the bottom of the header */
    left: 15px; /* Align with left padding of header */
    right: 15px; /* Align with right padding of header */
    height: 40px; /* Give it a fixed height */
}

.categories-scroll-container::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari, Opera */
}

#categories {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 5px 0; /* Adjusted padding */
    border-radius: 5px;
    height: 100%; /* Take full height of scroll container */
}

.category-btn {
    padding: 1px 13px;
    background-color: transparent; /* خلفية شفافة */
    color: white; /* لون النص أبيض */
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* خط طبيعي بدون بيكسل */
    font-size: 1rem; /* زيادة حجم النص قليلاً */
    font-weight: bold; /* جعل النص عريضًا */
    text-transform: capitalize; /* جعل أول حرف كبيرًا */
    letter-spacing: 0.5px; /* تقليل تباعد الأحرف */
    border: none; /* إزالة الحدود */
    border-radius: 20px; /* زوايا دائرية */
    cursor: pointer;
    transition: all 0.3s ease; /* حركة سلسة عند التفاعل */
    background-color: transparent !important; /* إزالة الخلفية */
    transition: transform 0.2s ease;
    font-family: 'Roboto', sans-serif; /* خط مميز مناسب */
    font-size: 1.2rem; /* زيادة حجم النص قليلاً */
    letter-spacing: 0; /* إزالة تباعد الأحرف */
}

.category-btn:hover {
    color: #ffcc00; /* لون النص عند التمرير (أصفر) */
    /* text-shadow removed */
}

/* Style for the active category button */
.category-btn.active-category {
    background-color: #ffcc00 !important; /* Orange background */
    color: black !important; /* Black text */
}

/* Ensure the active category button retains its style even when pressed/focused */
.category-btn.active-category:active,
.category-btn.active-category:focus,
.category-btn.active-category:focus-visible {
    background-color: #ffcc00 !important; /* Keep orange background */
    color: black !important; /* Keep black text */
    outline: none !important; /* Remove any focus outline */
    box-shadow: none !important; /* Remove any focus shadow */
}

/* --- Original Drawer Button Styles (Restored for index.html) --- */
.drawer-btn {
    width: 50px;
    height: 40px;
    font-size: 1.2rem;
    position: absolute;
    left: 15px;
    top: 10px; /* Position at the top of the header */
    background-color: var(--primary-color);
    color: white;
    padding: 5px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    z-index: 10;
    background-color: transparent !important;
    transition: transform 0.2s ease;
}

.drawer {
    position: fixed;
    top: 45px; /* Adjusted top for 45px fixed bar */
    left: -250px; /* البداية خارج الشاشة */
    width: 250px;
    height: calc(100% - 45px); /* Adjust height for 45px fixed bar */
    background: #000000; /* تغيير الخلفية إلى الأسود */
    color: white;
    display: flex; /* Reverted to flex */
    flex-direction: column; /* Reverted to flex */
    padding: 20px; /* Restored original padding */
    z-index: 10001; /* Increased z-index to be above buttons and top bar */
    justify-content: flex-start; /* Added back for flex */
    align-items: stretch; /* Let items stretch, alignment handled by margin */
    transition: left 0.3s ease, background 0.5s ease; /* إضافة انتقال للخلفية */
    box-shadow: none;
}

.drawer a {
    display: flex; /* Keep flex for icon/text alignment within link */
    align-items: center; /* Keep flex for icon/text alignment within link */
    gap: 10px; /* Space between icon and text */
    color: #ffffff; /* تغيير لون النص الافتراضي إلى الأبيض */
    text-decoration: none;
    padding: 15px 10px; /* Restored original padding */
    font-size: 1.1rem; /* Slightly smaller font size */
    margin-left: 0 !important; /* Force to left edge */
    margin-right: auto !important; /* Push from right */
    border-radius: 5px; /* Adjust border radius */
    transition: color 0.3s ease, background-color 0.3s ease, transform 0.2s ease;
}

.drawer a i { /* Style for the icons */
    width: 20px; /* Fixed width for icons */
    text-align: center;
    color: #ffcc00; /* Accent color for icons */
}

.drawer a:hover {
    color: #000000; /* Text color on hover */
    background-color: #ffcc00; /* Background color on hover */
    transform: translateX(5px); /* Slight move effect */
}

.drawer a:hover i {
    color: #000000; /* Icon color on hover */
}

.drawer.active {
    left: 0; /* تظهر القائمة */
}

/* --- Banner Ad Styles --- */
.banner-ad-container {
    width: 90%; /* Reduced width to 90% */
    margin: 20px auto; /* Added margin top/bottom and centered horizontally */
    position: relative;
    overflow: hidden;
    border-radius: 12px; /* Slightly increased border radius */
    height: 130px; /* Reduced height */
    box-shadow: none;
}

.banner-ad-slide {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    justify-content: center;
    align-items: center;
}

.banner-ad-slide.active {
    opacity: 1;
}

.banner-ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px; /* Match container border radius */
    cursor: pointer;
}

/* Gradient overlay at the bottom of the banner */
.banner-ad-slide::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px; /* Height of the gradient */
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    pointer-events: none; /* Make sure it doesn't interfere with clicks */
}

/* --- Banner Ad Modal Styles --- */
.banner-ad-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100005;
    animation: fadeIn 0.3s ease;
}

.banner-ad-modal-content {
    background-color: var(--card-background);
    width: 90%;
    max-width: 500px;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    position: relative;
}

.banner-ad-modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.banner-ad-modal-image {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    margin-bottom: 20px;
    border-radius: 10px;
}

.banner-ad-modal-title {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 10px;
}

.banner-ad-modal-description {
    color: #ccc;
    margin-bottom: 20px;
}

.banner-ad-modal-button {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: black;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.banner-ad-modal-button:hover {
    transform: scale(1.05);
}

/* Subscription Banner Badge */
.subscription-banner-badge {
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0% {
        transform: scale(1);
        box-shadow: none;
    }
    50% {
        transform: scale(1.05);
        box-shadow: none;
    }
    100% {
        transform: scale(1);
        box-shadow: none;
    }
}

/* Subscription Before Download Modal */
.subscription-before-download-modal {
    backdrop-filter: blur(10px);
}

.subscription-before-download-modal .subscription-modal-content {
    animation: modalSlideIn 0.4s ease-out;
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.subscription-before-download-modal .subscribe-btn:hover {
    transform: translateY(-2px);
    box-shadow: none;
}

.subscription-before-download-modal .skip-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Dynamic Modal Styles */
.dynamic-modal-overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100vw; height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex; justify-content: center; align-items: center;
  z-index: 2000;
  overflow-y: auto;
}
.dynamic-modal-box {
  background: var(--card-background);
  max-width: 90%; width: 500px;
  padding: 20px; border-radius: 12px;
  position: relative; color: var(--text-color);
}
.dynamic-modal-close {
  position: absolute; top: 10px; right: 10px;
  background: none; border: none;
  color: #fff; font-size: 1.5rem;
  cursor: pointer;
}
body.modal-open {
  overflow: hidden;
}

/* إعادة تعيين محدودة للتأكد من عدم تعارض النوافذ - بدون التأثير على التخطيط الأساسي */
body:not(.modal-open) {
  overflow: auto;
  pointer-events: auto;
}

html:not(.modal-open) {
  overflow: auto;
  pointer-events: auto;
}

/* --- New Category Section Styles --- */
.category-section {
    margin-bottom: 10px; /* Space between sections - Reduced from 25px */
    /* padding: 0 10px; */ /* Remove horizontal padding again */
    direction: ltr; /* Ensure section itself is LTR */
    text-align: left; /* Align text within section to left */
    /* background-color: var(--card-background); */ /* Reverted background color change */
}

/* ترتيب الأقسام بالترتيب الجديد المطلوب */
#news-section {
    order: 1; /* News - أول قسم */
}

#addons-section {
    order: 2; /* Addons - القسم الثاني (يتضمن Free Addons) */
}

#suggested-mods-section {
    order: 3; /* Suggested - القسم الثالث */
}

#shaders-section {
    order: 4; /* Shaders - القسم الرابع */
}

#texture-pack-section {
    order: 5; /* Texture Pack - القسم الخامس */
}

#seeds-section {
    order: 6; /* Seeds - القسم السادس */
}

#maps-section {
    order: 7; /* Maps - القسم السابع */
}

/* إضافة flex للحاوي الرئيسي لكي تعمل خاصية order */
#main-content-wrapper {
    display: flex;
    flex-direction: column;
}

/* أيقونة Free Addon */
.free-addon-icon {
    position: absolute;
    bottom: 15px;
    left: 5px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    padding: 2px 6px; /* Increased padding */
    border-radius: 3px;
    font-size: 0.7rem; /* Increased font size */
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.1px;
    box-shadow: none;
    z-index: 10;
    /* text-shadow removed */
    /* animation: growShrink 1.5s infinite alternate; */ /* Removed animation */
}

/* Free Addon icon glow animation removed for consistency with NEW badge */

/* أيقونة Popular للمودات الشعبية */
.popular-icon {
    position: absolute;
    bottom: 15px;
    right: 6px;
    background: linear-gradient(45deg, #FFD700, #FFA500); /* Golden yellow and orange */
    color: #FFF;
    padding: 3px 6px;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: none;
    border: 1px solid #FFD700; /* Keep golden yellow border */
    z-index: 10;
}

@keyframes popularGlow {
    0% {
        box-shadow: none;
        transform: scale(1);
    }
    100% {
        box-shadow: none;
        transform: scale(1.03); /* Increased scale slightly */
    }
}

@keyframes shimmerEffect {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}


/* تعريف أنيميشن اللمعان من اليسار إلى اليمين */
@keyframes shimmerLeftToRight {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(150%); /* Increased to ensure it fully exits the card */
    }
}



/* مربعات البيكسل المتوهجة لـ Free Addons */
.free-addon-pixel-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 1px;
    pointer-events: none;
    z-index: 5;
    animation: freeAddonPixelFloat 3s ease-out forwards;
    box-shadow: none;
}

@keyframes freeAddonPixelFloat {
    0% {
        opacity: 1; /* Reverted to original opacity */
        transform: translateY(0) scale(1); /* Reverted to original scale */
    }
    50% {
        opacity: 0.8; /* Reverted to original opacity */
        transform: translateY(-30px) scale(1.2); /* Reverted to original scale */
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.5); /* Reverted to original scale */
    }
}




/* مربعات البيكسل المتوهجة لـ Free Addons */
.free-addon-pixel-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 1px;
    pointer-events: none;
    z-index: 5;
    animation: freeAddonPixelFloat 3s ease-out forwards;
    box-shadow: none;
}

@keyframes freeAddonPixelFloat {
    0% {
        opacity: 1; /* Reverted to original opacity */
        transform: translateY(0) scale(1); /* Reverted to original scale */
    }
    50% {
        opacity: 0.8; /* Reverted to original opacity */
        transform: translateY(-30px) scale(1.2); /* Reverted to original scale */
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.5); /* Reverted to original scale */
    }
}

/* تأثيرات المودات الأكثر إعجاباً (أكثر من 1000 تحميل) */
.popular-mod {
    position: relative;
    border: 2px solid var(--accent-color); /* تغيير الحدود لتكون صفراء لتتناسب مع تأثير اللمعان */
    background: var(--card-background);
    animation: none; /* Remove popularModGlow animation */
    overflow: hidden; /* ضروري لتأثير اللمعان */
}

/* تطبيق تأثير اللمعان الأبيض على مودات Popular */

/* إزالة تأثير التحديد السيء */
.popular-mod:focus,
.popular-mod:active,
.popular-mod:hover {
    outline: none;
    transform: none;
    box-shadow: inherit; /* منع تغيير الظل عند التحديد */
}

/* إزالة تأثيرات التحديد من جميع كاردات المودات */
.mod-card:hover,
.item:hover,
.mod-card:focus,
.item:focus,
.mod-card:active,
.item:active {
    transform: none;
    box-shadow: inherit;
    outline: none;
}

/* تم تعديل هذا التعريف لإضافة تأثير اللمعان */
/* .popular-mod::before {
    display: none;
} */

/* Removed popularModGlow and borderRotate keyframes as they are no longer used */

/* مربعات البيكسل المتوهجة */
.pixel-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #FFD700, #FF8C00);
    border-radius: 1px;
    pointer-events: none;
    z-index: 5;
    animation: pixelFloat 3s ease-out forwards;
    box-shadow: none;
}

@keyframes pixelFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-30px) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.5);
    }
}


/* تم إزالة تعريف الأنيميشن shimmerLeftToRight لتحسين الأداء */

/* Align category headers */
.category-header {
    font-family: 'Roboto', sans-serif; /* خط مميز مناسب */
    font-size: 1.6rem; /* حجم خط مناسب */
    font-weight: bold; /* جعل النص عريضًا */
    text-transform: capitalize; /* جعل أول حرف كبيرًا */
    letter-spacing: 0; /* إزالة تباعد الأحرف */
    text-shadow: none;
    color: var(--text-color);
    margin-bottom: 20px;
    padding-left: 5px;
    padding-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    gap: 8px;
    direction: ltr;
    text-align: left;
}

/* Reduce gap specifically for News header */
#news-section .category-header {
    gap: 5px; /* تقليل المسافة لقسم الأخبار */
}

.category-icon { /* Added this rule back */
    width: 45px; /* Reverted to original size */
    height: 45px; /* Reverted to original size */
    vertical-align: middle; /* Align icon with text */
    position: relative; /* Allow positioning with top/bottom */
    top: 5px; /* Move icon down by 5px */
    /* margin-left: -2px; Removed negative margin */
    position: relative; /* Added for precise positioning if needed */
    top: -1px; /* Move up more significantly */
}

/* Specific rule for News icon */
#news-section .category-header .category-icon {
    width: 40px !important; /* Increased size, override inline style */
    height: 40px !important; /* Increased size, override inline style */
    top: 1px !important; /* Move up more significantly */
}

/* Specific rule for Gift icon to override inline style */
#suggested-mods-section .category-header .category-icon {
    top: 1px !important; /* Move up more significantly */
}

.category-header i { /* Style the dropdown icon */
    font-size: 1rem; /* Adjust icon size */
    color: var(--accent-color); /* Match accent color */
}

/* Style for the "see all" button */
.see-all-btn {
    margin-left: auto; /* Push the button to the right */
    font-size: 1.2rem; /* Increased font size to match category buttons */
    font-family: 'Roboto', sans-serif; /* Changed to Roboto to match category names */
    letter-spacing: 0; /* Removed letter spacing for consistency */
    color: var(--text-color); /* Changed to white */
    cursor: pointer;
    font-weight: normal; /* Make it less prominent than the header */
    padding: 3px 6px; /* Further reduced padding */
    border-radius: 5px; /* Optional: slightly rounded corners */
    transition: color 0.2s ease, background-color 0.2s ease;
    margin-top: 5px; /* Added to move button down slightly */
    letter-spacing: 0.5px; /* تباعد الأحرف للوضوح */
    /* text-shadow removed */
}

.see-all-btn img { /* Added this rule back */
    width: 16px; /* Adjust size */
    height: 16px; /* Adjust size */
    margin-left: 4px; /* Space between text and icon */
    vertical-align: middle; /* Align with text */
}


.see-all-btn:hover {
    color: var(--accent-color); /* Change hover color to yellow */
    background-color: rgba(255, 255, 255, 0.1); /* Subtle background on hover */
}

/* --- REMOVED: .mods-grid rule --- */
/* .mods-grid { ... } */

/* --- Re-added Horizontal Scroll Styles for "All" View --- */
.items-container {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    /* gap: 1.5rem; */ /* Removed gap */
    padding: 1rem 10px 1rem 0; /* Reduced vertical padding, Added right padding */
    max-width: 100%;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.items-container::-webkit-scrollbar {
    display: none;
}

.item { /* Style for horizontal items */
    flex: 0 0 280px; /* Fixed width for horizontal items */
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    border: 2px solid var(--accent-color); /* Add yellow border */
    padding: 1rem;
    /* cursor: pointer; */ /* Removed to potentially improve scroll behavior on touch */
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    direction: ltr; /* Ensure content inside is LTR */
    touch-action: pan-x pan-y; /* Allow horizontal and vertical panning */
    margin-right: 5px; /* Further reduced right margin */
    margin-left: 10px; /* Add left margin */
    position: relative; /* Added for shimmer effect */
    overflow: hidden; /* Added for shimmer effect */
}


/* Remove margin from the last item in the container */
.items-container .item:last-child {
    margin-right: 0;
}

.item:hover {
    transform: translateY(-5px);
    box-shadow: none;
}

/* تأثير التمرير على صور البطاقات الأفقية */
.item:hover .mod-image {
    transform: scale(1.02) !important;
}

.item .mod-image { /* Style image within horizontal item */
    width: 100%;
    height: 170px; /* تكبير الصورة قليلاً */
    object-fit: cover; /* Changed from center */
    border-radius: calc(var(--border-radius) - 4px);
    margin-bottom: 1rem;
    background-color: transparent !important; /* إزالة أي خلفية ملونة */
    transition: transform 0.3s ease !important;
}

.item .mod-name { /* Style name within horizontal item */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
    text-align: left;
    min-width: 0;
    font-size: 1.1rem; /* Slightly smaller font */
}

.item .mod-actions { /* Adjust actions for horizontal item */
    margin-top: auto; /* Push actions to bottom */
    padding-top: 0.5rem;
    border-top: none;
    justify-content: space-evenly;
}

/* Increased specificity to ensure these icons are 20px */
.item .action-item .action-icon {
    width: 20px;
    height: 20px;
}

.item .action-count {
    font-size: 0.9rem;
}
/* --- End Horizontal Scroll Styles --- */

.drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 9998;
}

.drawer-overlay.active {
    visibility: visible;
    opacity: 1;
}

.drawer-btn img {
    width: 33px;
    height: 33px;
    object-fit: contain;
}

/* النافذة تغطي الشاشة بالكامل (مع الأخذ في الاعتبار الشريط العلوي الثابت) */
.modal {
    display: none;
    position: fixed;
    top: 45px; /* Adjusted top for 45px fixed bar */
    left: 0;
    width: 100%;
    height: calc(100% - 45px); /* Adjust modal height for 45px fixed bar */
    background-color: rgba(0, 0, 0, 0.8); /* خلفية داكنة لتمييز النافذة */
    z-index: 99999; /* Keep high z-index, but positioning handles overlap */
    animation: fadeIn 0.3s ease;
    overflow-y: auto; /* تمكين التمرير عند الحاجة */
    pointer-events: auto; /* التأكد من أن النافذة تستجيب للنقر */
}

/* التأكد من أن النافذة تظهر بشكل صحيح عند فتحها */
.modal[style*="display: block"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* محتوى النافذة */
.modal-content {
    background-color: var(--card-background);
    width: 100%; /* Ensure it takes full width */
    max-width: 100%; /* Allow it to expand fully */
    margin: 0 auto;
    height: auto;
    min-height: 100%; /* تغطية جزء كبير من الشاشة */
    padding: 0; /* Remove all padding to expand content */
    border-radius: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    animation: slideIn 0.3s ease; /* تأثير جميل عند الظهور */
    padding-bottom: 95px; /* Adjusted for fixed bottom bar in modal (85px height + 10px buffer) */
    overflow-y: auto; /* Ensure content can scroll */
}

/* الصورة الرئيسية - محاذاة مع المحتوى النصي */
.main-image {
    width: calc(100% - 30px) !important; /* نفس هوامش المحتوى النصي (15px من كل جانب) */
    max-width: calc(100% - 30px) !important; /* فرض الحد الأقصى للعرض */
    max-height: 350px !important; /* تكبير الصورة قليلاً */
    object-fit: cover !important;
    border-radius: 15px !important; /* حواف دائرية أكثر عصرية */
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
    margin: 15px auto 1rem !important; /* هوامش متناسقة مع المحتوى */
    display: block !important;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2) !important; /* ظل ذهبي وظل عمق */
    height: auto !important; /* ارتفاع تلقائي */
    background-color: transparent !important; /* خلفية شفافة */
    border: 3px solid #ffd700 !important; /* إطار ذهبي */
}

/* الصور المصغرة أسفل الصورة الرئيسية - محاذاة مع المحتوى */
.thumbnail-container {
    display: flex;
    flex-wrap: nowrap; /* Ensure images stay in a single row */
    justify-content: flex-start; /* محاذاة لليسار بدلاً من الوسط */
    gap: 15px;
    margin: 0 15px 1.5rem 15px; /* نفس هوامش المحتوى النصي */
    overflow-x: auto; /* تمرير أفقي عند وجود الكثير من الصور */
    padding: 0; /* إزالة المساحة الداخلية الإضافية */
    /* Hide scrollbar visually */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
}
/* Hide scrollbar for Chrome, Safari and Opera */
.thumbnail-container::-webkit-scrollbar {
    display: none;
}


.thumbnail-container img {
    flex-shrink: 0; /* Prevent images from shrinking */
    width: 70px;
    height: 70px;
    object-fit: cover;
    border: 3px solid #ffd700; /* إطار ذهبي مثل الصورة الرئيسية */
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease; /* إضافة تأثير الظل */
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.4), 0 2px 6px rgba(0, 0, 0, 0.15); /* ظل ذهبي خفيف */
}

.thumbnail-container img:hover {
    border-color: #ffb700; /* ذهبي أغمق عند التمرير */
    transform: scale(1.05);
    box-shadow: 0 0 12px rgba(255, 215, 0, 0.6), 0 3px 10px rgba(0, 0, 0, 0.2); /* ظل ذهبي أقوى */
}

.thumbnail-container img.selected {
    border-color: #ffb700; /* ذهبي أغمق للمحدد */
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.7), 0 4px 12px rgba(0, 0, 0, 0.25); /* ظل ذهبي قوي */
}

/* عدد التحميلات والإعجابات */
.stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    color: #4caf50;
}

/* الوصف */
.description-container {
    /* text-align: center; */ /* Changed to left */
    text-align: left;
    margin-bottom: 1.5rem;
}

.description {
    /* max-height: 60px; */ /* Removed height limit */
    /* overflow: hidden; */ /* Removed overflow hidden */
    /* text-overflow: ellipsis; */ /* Removed ellipsis */
    white-space: normal; /* Allow text wrapping */
    color: var(--text-color); /* Use theme text color */
    line-height: 1.5;
    text-align: left; /* Align text to the left for readability */
    padding: 0; /* Remove all horizontal padding to expand content */
}

/* .read-more-btn { ... } */ /* Removed - button likely not needed anymore */

/* الحجم والإصدار */
.details {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    font-weight: bold;
    color: #555;
}

/* زر التحميل كنافذة عائمة */
.download-bar {
    position: fixed;
    bottom: 0; /* Default position */
    left: 0;
    width: 100%;
    background-color: #4caf50; /* Corrected color value */
    padding: 10px 15px; /* Reverted padding */
    padding-bottom: calc(10px + env(safe-area-inset-bottom, 0px)); /* Adjusted bottom padding + safe area */
    min-height: 100px; /* Add minimum height */
    display: flex; /* Use flexbox for layout */
    justify-content: center; /* Center download button by default */
    align-items: center; /* Center items vertically */
    gap: 10px; /* Add gap between potential buttons */
    box-shadow: none;
    z-index: 100001; /* Ensure it's above modal content */
}

.download-btn {
    background: linear-gradient(45deg, #ffd700, #ffcc00); /* خلفية ذهبية */
    color: white; /* لون النص */
    font-size: 1.2rem; /* حجم النص */
    font-weight: bold; /* خط عريض */
    border: none; /* إزالة الحدود */
    border-radius: 10px; /* زوايا مدورة */
    padding: 12px 25px; /* حجم الزر */
    cursor: pointer; /* مؤشر اليد */
    box-shadow: none;
    transition: transform 0.15s ease-out, box-shadow 0.15s ease-out;
    text-align: center;
}

/* تأثير الضغط */
.download-btn:active {
    transform: scale(0.95); /* تصغير الزر عند الضغط */
    box-shadow: none;
}

/* Ensure download button can contain the absolute positioned progress */
.download-btn {
    position: relative; /* Needed for absolute positioning of children */
    overflow: hidden;   /* Hide parts of the progress bar that might overflow */
}

/* Style for the button when the mod is downloaded */
.download-btn.downloaded {
    background-color: #5cb85c; /* Green background for downloaded state */
    /* Optional: Add other styles like border */
}
.download-btn.downloaded:hover {
    background-color: #4cae4c; /* Darker green on hover */
}

/* Progress Indicator Styles */
.download-progress-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* background-color: rgba(0, 0, 0, 0.6); /* Dark overlay */
    background-color: rgba(255, 215, 0, 0.9); /* Yellow background */
    box-shadow: none;
    display: flex; /* Use flex to center text */
    align-items: center;
    justify-content: center;
    border-radius: 10px; /* Match button's border-radius */
    z-index: 2; /* Ensure it's above the button's text/icon */
    /* display: none; /* Initially hidden - handled by JS */
}

.download-progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%; /* Initial width */
    /* background-color: #4CAF50; /* Green progress bar - Removed */
    background: linear-gradient(to right, #FFA500, #FFD700); /* Orange to Golden Yellow gradient */
    border-radius: 10px; /* Match button's border-radius */
    transition: width 0.1s linear; /* Smooth width transition */
    z-index: 1; /* Below the text */
}

.download-progress-text {
    position: relative; /* Ensure text is above the progress bar */
    z-index: 2;
    color: white;
    font-size: 14px;
    font-weight: bold;
    /* text-shadow removed */
}
/* End Progress Indicator Styles */


button:hover {
    background-color: #f5f5f5;
}

#details-modal {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} /* End of @keyframes fadeIn */

/* زر الإغلاق (Keep existing styles, ensure it doesn't conflict) */
.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
    font-size: 1.5rem;
    color: #64748b;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* الزر يبقى دائريًا */
    background-color: transparent; /* إزالة الخلفية البيضاء */
    box-shadow: none; /* إزالة الظل إذا كان يسبب المشكلة */
}

.close-btn:hover {
    background-color: #e2e8f0;
    color: #333;
}

/* New Close Button on Modal Image */
.modal-image-close-btn {
    position: absolute;
    top: 10px; /* Adjust as needed */
    right: 10px; /* Adjust as needed */
    background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;  /* Increased size */
    height: 35px; /* Increased size */
    font-size: 1.8rem; /* Increased font size */
    line-height: 1; /* Center the '×' */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10; /* Ensure it's above the image */
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.modal-image-close-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.modal-image-close-btn:active {
    transform: scale(0.95);
}

.modal-image-close-btn img {
    width: 36px; /* Increased size */
    height: 36px; /* Increased size */
    display: block; /* Ensure proper rendering */
}

/* زر الإعجاب */
.like-button {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: none;
}

.like-button .heart-icon {
    width: 24px;  /* Changed from 30px */
    height: 24px; /* Changed from 30px */
    transition: color 0.3s ease;
}

.thumbnail-container img:active {
    transform: scale(1.2);
}

/* تنسيق عام للأزرار */
#button-1 {
    padding: 10px 20px;
    border: none;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.1s ease-in-out;
}

        @keyframes fadeIn {
            from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* --- Animation for page load (Fade-in from Bottom) --- */
.initial-hidden-animate {
    opacity: 0;
    transform: translateY(20px); /* Start slightly lower */
    /* filter: blur(5px); */ /* Removed blur */
    transition: opacity 0.4s ease-out, transform 0.4s ease-out; /* Removed filter transition */
}

.initial-hidden-animate.animate-visible {
    opacity: 1;
    transform: translateY(0);
    /* filter: blur(0); */ /* Removed blur */
}
/* --- End Animation for page load --- */

    #modalContent p {
        overflow-wrap: break-word; /* كسر الكلمات الطويلة */
        word-wrap: break-word; /* دعم المتصفحات القديمة */
        word-break: break-word; /* كسر النص عند الحدود */
        white-space: normal; /* السماح بتعدد الأسطر */
    }

/* --- Original Search Button Styles (Restored for index.html) --- */
.search-btn {
    position: absolute;
    right: 15px;
    top: 10px; /* Position at the top of the header */
    background-color: var(--primary-color);
    padding: 5px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    z-index: 100;
    transition: background-color 0.3s ease;
    background-color: transparent !important;
    transition: transform 0.2s ease;
}

.search-btn:hover {
    background-color: var(--secondary-color); /* Restored */
}

.search-btn img { /* Restored */
    width: 34px; /* Restored */
    height: 34px; /* Restored */
    object-fit: contain; /* Restored */
}


@keyframes likeBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.4); }
    100% { transform: scale(1); }
}

/* تأثير عند الضغط على زر الإعجاب */
button.like-button:active .heart-icon {
    animation: likeBounce 0.3s ease-out;
}

/* عند الإعجاب، يصبح القلب أحمر */
button.like-button.liked .heart-icon {
    color: red;
    transition: color 0.3s ease;
}

.downloads-icon {
    color: gold;
}

.mod-card { /* Styles for vertical list items AND grid items */
    color: white;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    border: 2px solid var(--accent-color); /* Add yellow border */
    padding: 1rem;
    transition: transform 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    touch-action: pan-y; /* Explicitly allow vertical panning */
    /* margin: 10px; */ /* Use gap from container instead */
    margin-left: 10px; /* Add left margin */
    margin-bottom: 10px; /* Add bottom margin for spacing in vertical lists */
    position: relative; /* ضروري لتأثير اللمعان */
}

/* تم إزالة تأثير اللمعان العام وتركه فقط للمودات الشعبية */


/* New badge for recent mods */
.mod-image-container {
    position: relative;
    width: 100%;
    height: 170px; /* تكبير حاوية الصورة قليلاً */
    overflow: hidden; /* Ensure badge doesn't overflow */
    border-radius: calc(var(--border-radius) - 4px); /* Match image border-radius */
    margin-bottom: 1rem; /* Match original image margin */
    background-color: transparent !important; /* إزالة أي خلفية ملونة */
}

.mod-image-container .mod-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: calc(var(--border-radius) - 4px);
    background-color: transparent !important; /* إزالة أي خلفية ملونة */
    transition: transform 0.3s ease !important;
}

.new-badge {
    position: absolute;
    bottom: 15px; /* Position from bottom */
    right: 5px; /* Position from right */
    background: linear-gradient(135deg, #FFD700, #FFA500); /* Same gradient as FREE ADDON */
    color: white;
    padding: 2px 5px; /* Increased padding */
    border-radius: 3px;
    font-size: 0.65rem; /* Increased font size */
    font-weight: bold;
    z-index: 10;
    box-shadow: none;
    text-transform: uppercase;
    letter-spacing: 0.1px;
    /* text-shadow removed */
    /* animation: growShrink 1.5s infinite alternate; */ /* Removed animation */
}

/* تأثير اللمعان للمودات الشعبية في جميع التصنيفات */
.popular-mod::before,
.item.popular-mod::before,
.mod-card.popular-mod::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        transparent 30%,
        rgba(255, 255, 255, 0.9) 50%, /* زيادة شفافية اللمعان */
        transparent 70%,
        transparent 100%
    );
    transform: translateX(100%);
    z-index: 2; /* زيادة z-index للتأكد من ظهوره فوق العناصر الأخرى */
    pointer-events: none;
    animation: flashSweep 3s ease-in-out infinite;
    animation-delay: 1s; /* إضافة تأخير للتأثير ليبدأ بعد تحميل الصفحة */
    display: block !important; /* التأكد من ظهور التأثير */
}

@keyframes flashSweep {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    5% { /* تسريع ظهور اللمعان */
        opacity: 1;
    }
    25% { /* تسريع حركة اللمعان */
        transform: translateX(-100%);
        opacity: 1;
    }
    30% { /* تسريع اختفاء اللمعان */
        opacity: 0;
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }
}

/* New specific rule for centering cards in single category view */
#singleCategoryContainer .mod-card {
    margin-left: auto;   /* Override general margin-left from .mod-card */
    margin-right: auto;  /* Add margin-right to auto for centering */
    width: 98%;          /* Make cards 98% of container width, allowing auto margins to center them. */
                         /* The general .mod-card rule provides margin-bottom: 10px for vertical spacing. */
}

/* Remove bottom margin for the last card in a single category view to avoid extra space at the end */
#singleCategoryContainer .mod-card:last-child {
    margin-bottom: 0;
}

.mod-name { /* General styles */
    font-size: 1.25rem;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 10px;
    text-align: left; /* Keep left alignment for vertical cards */
    width: 100%;
    min-width: 0;
}

.mod-info { /* Added style for the info container */
    width: 100%; /* Ensure it takes full width */
    display: flex; /* Use flex for its children */
    flex-direction: column; /* Stack name and actions vertically */
    align-items: stretch;
    margin-top: 10px; /* Add some space below the image */
}

.mod-card:hover {
    transform: translateY(-5px);
    box-shadow: none;
}

/* تأثير التمرير على صور البطاقات العمودية */
.mod-card:hover .mod-image,
.mod-card:hover .mod-image-container .mod-image {
    transform: scale(1.02) !important;
}

/* صورة المود */
.mod-image {
    width: 100%;
    height: 220px; /* تكبير الصورة قليلاً */
    object-fit: cover;
    border-radius: calc(var(--border-radius) - 4px);
    background-color: transparent !important; /* إزالة أي خلفية ملونة */
    transition: transform 0.3s ease !important;
}

/* قسم الإعجابات والتحميلات */
.mod-actions {
    display: flex;
    justify-content: space-evenly; /* Changed to space-evenly for equal spacing */
    align-items: center;
    width: 100%;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.action-icon {
    width: 24px;
    height: 24px;
}

.action-count {
    font-size: 1rem;
    color: var(--text-color);
}

/* أيقونة منصة ماين كرافت */
.minecraft-icon {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ستايل مؤشر جاري التحميل */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #ffcc00;
    font-weight: bold;
    padding: 15px;
    opacity: 0.9;
    animation: fadeBlink 1s infinite alternate;
}

/* دائرة التحميل المتحركة */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 204, 0, 0.3);
    border-top-color: #ffcc00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

/* تأثير الوميض */
@keyframes fadeBlink {
    from {
        opacity: 0.5;
    }
    to {
        opacity: 1;
    }
}

/* دوران دائرة التحميل */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeBlink {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* الرسوم المتحركة لتعبئة القلب */
@keyframes fillHeart {
    0% {
        background-color: transparent;
        color: black;
        transform: scale(1);
    }
    50% {
        background-color: rgba(255, 0, 0, 0.5);
        transform: scale(1.2);
    }
    100% {
        background-color: red;
        color: white;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Initial state REMOVED */
/* .header .drawer-btn, ... */
/* { ... } */

/* .fade-in class REMOVED */
/* .fade-in { ... } */


/* Animation properties for mod cards and items removed for performance */

/* --- Adjusted Animation Delay Selectors --- */
/* تأخير ظهور كل عنصر ليحدث التأثير بالتدريج */
/* nth-child delays removed, JS will handle dynamic delays */
/* --- End Adjusted Animation Delay Selectors --- */

/* أنيميشن للأزرار */
/* General button animation properties moved to .fade-in class for specific buttons */
/* button { */
    /* opacity: 0; */
    /* transform: scale(0.8); */
    /* animation: fadeInUp 0.4s ease-in-out forwards; */
    /* animation-delay: 0.5s; */
/* } */ /* Commented out to avoid conflict with new load animation */

/* تطبيق الرسوم المتحركة */
button.like-button.animate-heart {
    position: relative;
    overflow: hidden;
    border-radius: 50%; /* لتجنب overflow */
    animation: fillHeart 0.6s ease forwards;
}

/* التأثير على أيقونة القلب */
button.like-button.animate-heart .heart-icon {
    color: white;
    transition: color 0.6s ease;
}


/* تحسين تمرير الصور */
div[style*="overflow-x: auto"] {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    /* Hide scrollbar visually */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
}
/* Hide scrollbar for Chrome, Safari and Opera */
div[style*="overflow-x: auto"]::-webkit-scrollbar {
    display: none;
}


div[style*="overflow-x: auto"] img {
    scroll-snap-align: start;
    border-radius: 10px;
}


        .more-text { display: none; }
        .toggle-btn {
            color: yellow;
            cursor: pointer;
            text-decoration: underline;
        }

        .close-btn {
    position: absolute;
    top: 10px; /* رفع الزر للأعلى */
    right: 15px; /* تعديل المسافة عن اليمين */
    width: 48px; /* زيادة عرض الزر قليلا */
    height: 48px; /* زيادة ارتفاع الزر قليلا */
    background-image: url('image/close2.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain; /* Or adjust to a percentage like 80% if needed */
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: transform 0.2s ease, opacity 0.2s ease;
    /* Hide text if any is present in the HTML element */
    font-size: 0;
    text-indent: -9999px;
    overflow: hidden;
}

.close-btn:hover {
    transform: scale(1.2); /* تكبير عند التحويم */
    opacity: 0.7;
}

.help-icon {
    width: 35px; /* تكبير الأيقونة */
    height: 35px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.help-icon:hover {
    transform: scale(1.2); /* تكبير عند التحويم */
}

/* --- Sorting Buttons Styles (Re-added/Ensured) --- */
.sort-buttons {
    display: none; /* Hide by default */
    justify-content: center; /* Center buttons horizontally */
    gap: 10px; /* Add space between buttons */
    /* flex-wrap: wrap; */ /* Removed to keep buttons on one line */
    margin: 15px 0; /* Add margin above and below */
    padding: 0 10px; /* Add horizontal padding */
}

.sort-btn {
    background-color: transparent;
    color: #ff9800;
    border: 2px solid #ff9800;
    padding: 6px 11px;
    border-radius: 20px;
    font-size: 1rem; /* زيادة حجم الخط قليلاً */
    font-family: 'Roboto', sans-serif; /* خط مميز مناسب */
    cursor: pointer;
    font-weight: bold;
    white-space: nowrap;
    transition: background 0.3s ease, color 0.3s ease, transform 0.2s ease;
    text-align: center;
    letter-spacing: 0; /* إزالة تباعد الأحرف */
}

.sort-btn:hover {
    background-color: #ff9800;
    color: white;
    transform: scale(1.05);
}

.sort-btn.active-sort { /* Style for the active sort button */
    background-color: #ff9800;
    color: white;
    border-color: #ff9800; /* Ensure border color matches */
}
/* --- End Sorting Buttons Styles --- */

/* --- Network Status Indicator Styles --- */
.network-status-indicator {
    position: fixed;
    top: 50px; /* Below the top fixed bar */
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    z-index: 100002;
    box-shadow: none;
    display: none;
    animation: slideDown 0.3s ease;
}

.network-status-indicator.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.network-status-indicator.warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.network-status-indicator.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Offline Mode Styles */
.offline-mode {
    filter: grayscale(0.3);
    opacity: 0.9;
}

.offline-banner {
    position: fixed;
    top: 45px;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, #607d8b, #455a64);
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 0.9rem;
    z-index: 100001;
    box-shadow: none;
}

/* Retry Button Styles */
.retry-btn {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: bold;
    cursor: pointer;
    margin-left: 10px;
    transition: transform 0.2s ease;
}

.retry-btn:hover {
    transform: scale(1.05);
}

.retry-btn:active {
    transform: scale(0.95);
}
/* --- End Network Status Indicator Styles --- */


/* Search Input Styles */
.search-container {
    padding: 15px 20px; /* Add some padding around the search bar */
    margin-bottom: 10px; /* Space below the search bar */
}

.search-input {
    width: 100%;
    padding: 12px 15px;
    font-size: 1rem;
    border: 2px solid var(--accent-color); /* Yellow border */
    border-radius: var(--border-radius);
    background-color: var(--card-background); /* Match card background */
    color: var(--text-color); /* White text */
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.search-input::placeholder {
    color: #aaa; /* Lighter placeholder text */
}

.search-input:focus {
    outline: none; /* Remove default focus outline */
    box-shadow: none;
}

/* --- REMOVED: #moreCategoriesContainer Styles --- */
/* #moreCategoriesContainer { ... } */
/* #moreCategoriesContainer .category-btn { ... } */


/* --- إزالة تأثيرات النقر والتركيز الافتراضية --- */

/* القاعدة العامة لإزالة لون التحديد عند اللمس (للموبايل) */
* {
    -webkit-tap-highlight-color: transparent;
}

/* إزالة الإطار (outline) عند التركيز (focus) لجميع العناصر التفاعلية */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
    outline: none !important;
    box-shadow: none !important; /* إزالة أي ظل قد يظهر عند التركيز */
}

/* Adjust :active/:focus-visible styles to prevent overriding .active-category */
/* These should generally not set background color to avoid conflicts */
a:active, a:focus-visible,
button:not(.active-category):not(.active-sort):active,
button:not(.active-category):not(.active-sort):focus-visible,
input:active, input:focus-visible,
select:active, select:focus-visible,
textarea:active, textarea:focus-visible,
[tabindex]:active, [tabindex]:focus-visible {
    color: inherit; /* Prevent unexpected text color changes */
    /* background-color: transparent; <-- Keep removed */
}

/* Removed .category-btn:active rule, will use JS class for press effect */

/* Class for button press effect via JS */
.button-pressed {
    transform: scale(0.98) !important; /* Apply press effect */
}

/* --- أنيميشن الأزرار --- */

/* أنيميشن عام للأزرار عند النقر */
button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

button:active::after {
    width: 200px;
    height: 200px;
}

/* أنيميشن خاص لأزرار التصنيفات */
.category-btn {
    position: relative;
    overflow: hidden;
}

.category-btn::before {
    display: none; /* تم تعطيل الخط الأصفر */
}

/* أنيميشن لزر التحميل */
.download-btn {
    position: relative;
    overflow: hidden;
}

.download-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 25%,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 50%,
        transparent 75%,
        rgba(255, 255, 255, 0.1) 75%
    );
    transform: rotate(45deg);
}


/* --- Search Page Specific Header Styles --- */
.search-page .header {
    height: 60px; /* Adjusted height for search page */
    flex-direction: row; /* Override to row */
    justify-content: space-between; /* Space out items */
    padding: 0 15px; /* Horizontal padding */
    margin-bottom: 15px; /* Reduced margin for search page */
}

/* Hide drawer button on search page */
.search-page .drawer-btn {
    display: none;
}

/* Style back button specifically for search page */
.search-page .back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.8rem; /* Larger icon */
    cursor: pointer;
    padding: 10px; /* Touch area */
    display: flex; /* Center icon inside button */
    align-items: center;
    justify-content: center;
    line-height: 1; /* Ensure icon is centered vertically */
    order: -1; /* Ensure it comes first in flex row */
}

/* Adjust title for search page header */
.search-page .header h1 {
    font-size: 1.5rem; /* Slightly smaller title */
    margin-bottom: 0; /* Remove bottom margin */
    flex-grow: 1; /* Allow title to take space */
    text-align: center; /* Center title text */
    margin: 0 10px; /* Add some margin around title */
}

/* Ensure search button (though hidden) doesn't interfere with layout */
.search-page .search-btn {
    position: static; /* Remove absolute positioning */
    transform: none; /* Remove transform */
    padding: 10px; /* Match back button padding */
    /* visibility: hidden; is handled inline */
}

/* Hide categories on search page header */
.search-page #categories {
    display: none;
}
/* --- End Search Page Specific Styles --- */

/* --- New Install Instructions Modal Styles --- */
#install-instructions-modal {
    position: fixed;
    top: 45px !important; /* Start below the top bar (Forced) */
    left: 0;
    width: 100%;
    height: calc(100% - 45px) !important; /* Adjust height to fill remaining space (Forced) */
    background-color: #333333; /* Dark Gray background */
    display: flex; /* Use flex for layout */
    flex-direction: column; /* Stack header and content vertically */
    z-index: 100003; /* Ensure it's above everything */
    animation: fadeIn 0.3s ease; /* Reuse existing fadeIn */
    box-sizing: border-box;
}

.modal-install-header {
    width: 100%;
    height: 60px; /* Adjust height as needed */
    background: linear-gradient(to right, #ffcc00, #ff9800); /* Theme gradient */
    display: flex;
    align-items: center;
    padding: 0 15px; /* Padding */
    flex-shrink: 0; /* Prevent header from shrinking */
    box-sizing: border-box;
}

#install-instructions-modal .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 2.5rem; /* Larger '×' */
    font-weight: bold;
    line-height: 1;
    padding: 5px 10px; /* Touch area */
    cursor: pointer;
    margin-right: auto; /* Push to the left */
    transition: transform 0.2s ease;
}
#install-instructions-modal .close-button:hover {
    transform: scale(1.1);
}
#install-instructions-modal .close-button:active {
    transform: scale(0.95);
}

#install-image-container {
    width: 100%;
    flex-grow: 1; /* Take remaining vertical space */
    overflow-y: auto; /* Enable vertical scrolling */
    padding: 20px; /* Padding around images */
    box-sizing: border-box;
    display: flex;
    flex-direction: column; /* Stack images vertically */
    align-items: center; /* Center images horizontally */
    gap: 15px; /* Space between images */
    touch-action: pan-y; /* Explicitly allow vertical panning/scrolling */
}

#install-image-container img {
    display: block;
    max-width: 90%; /* Constrain image width to make it smaller */
    height: auto; /* Maintain aspect ratio */
    border: 3px solid #ffcc00; /* Yellow border */
    border-radius: 5px; /* Slightly rounded corners */
    cursor: pointer; /* Indicate images are clickable */
    transition: transform 0.2s ease; /* Add transition for hover effect */
}

#install-image-container img:hover {
    transform: scale(1.03); /* Slight zoom on hover */
}
/* --- End New Install Instructions Modal Styles --- */

/* --- Image Zoom Modal Styles --- */
#image-zoom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}

/* Reduce margin below the header for the "All" category section (or similar horizontal sections) */
/* Please adjust #all-mods-section if the ID for your "All" category is different */
#all-mods-section .category-header {
    margin-bottom: 10px; /* Default is 20px */
}

/* --- Featured Addons Styles --- */
.featured-addon {
    position: relative;
    border: 2px solid #ffcc00 !important;
    box-shadow: none !important;
    overflow: visible !important;
}

.featured-addon::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: var(--border-radius);
    background: transparent;
    z-index: -1;
    box-shadow: none;
}

/* Yellow orbs animation */
.featured-addon .orb {
    position: absolute;
    width: 10px;
    height: 10px;
    background: rgba(255, 204, 0, 0.8);
    border-radius: 50%;
    z-index: 10;
    pointer-events: none;
    box-shadow: none; /* Removed glow effect */
    opacity: 0;
}



@keyframes orbFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.8);
    }
    50% {
        opacity: 1;
        transform: translateY(-15px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px) scale(0.8);
    }
}

/* Glowing Square Effect */
.glowing-square-effect {
    position: absolute;
    width: 12px; /* Slightly larger for squares */
    height: 12px;
    background: linear-gradient(45deg, #ffd700, #ff9800); /* Golden-orange gradient */
    border-radius: 2px; /* Small border-radius for slightly rounded squares */
    pointer-events: none;
    z-index: 5;
    box-shadow: none;
    opacity: 0; /* Start hidden */
    animation: glowingSquareFloat 3s ease-out forwards; /* New animation */
}

@keyframes glowingSquareFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.5) rotate(0deg);
    }
    20% {
        opacity: 1;
        transform: translateY(-10px) scale(1) rotate(45deg);
    }
    80% {
        opacity: 1;
        transform: translateY(-40px) scale(1.2) rotate(90deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.8) rotate(135deg);
    }
}

/* Sparkle Effect - Re-purposed for additional glow */
.glowing-sparkle-effect {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #ffcc00; /* Golden color */
    border-radius: 50%; /* Keep as circles for a different sparkle look */
    pointer-events: none;
    animation: glowingSparkleAnimation 2s ease-in-out infinite;
    z-index: 6;
    box-shadow: none;
}

@keyframes glowingSparkleAnimation {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
        box-shadow: none;
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
        box-shadow: none;
    }
}



/* Remove Free Addons specific styling as it will be replaced by general glowing squares */
/* .free-addon-item, .free-addon-item::before, .free-addon-item:hover,
   @keyframes freeAddonGlow, @keyframes borderGlow,
   .free-badge, @keyframes freeBadgePulse,
   .free-icon, .free-icon::before, @keyframes freeIconPulse {
    display: none;
} */
/* The above commented out block is too broad and might remove other necessary styles.
   Instead, I will remove the specific rules that were previously defined for these.
   The user wants the effect on *all* cards, so the "free-addon-item" class and its
   associated effects are no longer needed. */

/* Remove Free Addons Special Effects */
.free-addon-item {
    /* No special styling for free-addon-item anymore */
    position: relative;
    overflow: hidden;
    background-color: var(--card-background) !important;
    border-radius: var(--border-radius) !important;
    transition: var(--transition);
}

.free-addon-item:hover {
    transform: translateY(-5px);
}

/* Remove Free Badge and Free Icon styles */
.free-badge, .free-icon {
    display: none; /* Hide these elements */
}

/* Remove Free Addons Section Header Enhancement - already done in previous step */
/* #free-addons-section .category-header {
    position: relative;
    overflow: hidden;
} */

@keyframes growShrink {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Custom Dialog Animations */
@keyframes customDialogFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
}

@keyframes customDialogSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Custom Dialog Styles */
.custom-dialog-overlay {
    animation: customDialogFadeIn 0.3s ease;
}

.custom-dialog-content {
    animation: customDialogSlideIn 0.4s ease;
}

/* Help Icon Styles */
.help-icon {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #ffcc00, #ff9800);
    border: none;
    border-radius: 50%;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: none;
    opacity: 1;
    transform: scale(1);
}

.help-icon:hover {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    box-shadow: none;
    transform: scale(1.05);
}

.help-icon:active {
    transform: scale(0.95);
}

/* Help Icon Pulse Animation */
@keyframes helpIconPulse {
    0% {
        box-shadow: none;
        transform: scale(1);
    }
    50% {
        box-shadow: none;
        transform: scale(1.1);
    }
    100% {
        box-shadow: none;
        transform: scale(1);
    }
}

.help-icon-pulse {
    animation: helpIconPulse 0.6s ease-in-out;
}

/* Help Icon Glow Effect */
.help-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #ffcc00, #ff9800);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.help-icon:hover::before {
    opacity: 0.3;
}

/* Responsive Help Icon */
@media (max-width: 768px) {
    .help-icon {
        width: 30px;
        height: 30px;
        font-size: 14px;
        top: 10px;
        left: 10px;
    }
}


/* Floating Subscription Icon Styles */
.floating-icon {
    position: fixed;
    right: 20px; /* Position from the right */
    top: 50%; /* Vertically center */
    transform: translateY(-50%) translateX(100px); /* Start off-screen to the right */
    width: 70px; /* Increased size for better visibility */
    height: 70px;
    background: linear-gradient(135deg, #ffd700, #ffcc00); /* Golden gradient background */
    border-radius: 50%; /* Circular shape */
    border: 3px solid #fff; /* White border for contrast */
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100000; /* High z-index to be on top */
    transition: transform 0.5s ease-out;
    animation: slideInFromRight 0.8s ease-out forwards, pulse 2s infinite; /* Animation on load + pulse */
    animation-delay: 2s; /* Delay animation to appear after app loads */
    opacity: 0; /* Start hidden for animation */
}

.floating-icon.hidden {
    display: none; /* Use this class to hide it completely when disabled */
}

.floating-icon:hover {
    transform: translateY(-50%) scale(1.1); /* More noticeable enlargement on hover */
    box-shadow: none;
    animation-play-state: paused; /* Pause pulse animation on hover */
}

.floating-icon-image {
    width: 85%; /* Image takes 85% of the icon's width */
    height: 85%; /* Image takes 85% of the icon's height */
    object-fit: contain; /* Ensure the image fits without cropping */
    border-radius: 50%; /* Keep image circular */
    filter: none;
}

/* Animation for the floating icon */
@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateY(-50%) translateX(100px);
    }
    70% {
        opacity: 1;
        transform: translateY(-50%) translateX(-10px); /* Slight overshoot */
    }
    100% {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

/* Pulse animation for floating icon */
@keyframes pulse {
    0% {
        box-shadow: none;
    }
    50% {
        box-shadow: none;
    }
    100% {
        box-shadow: none;
    }
}

/* Responsive adjustments for floating icon */
@media (max-width: 768px) {
    .floating-icon {
        right: 10px; /* Adjust position for smaller screens */
        width: 50px; /* Smaller size for mobile */
        height: 50px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .category-btn {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .item {
        width: calc(50% - 10px);
    }


@media (max-width: 768px) {
    /* ... بعض القواعد الأخرى ... */
    .modal-content {
        width: 100%;      /* غيرنا العرض إلى 100% ليملأ الشاشة */
        max-width: 100%;  /* تأكيد على أن أقصى عرض هو 100% */
        padding: 0;       /* أزلنا المساحة الداخلية للسماح للمحتوى بالوصول للحواف */
        padding-bottom: 95px; /* أبقينا على المساحة السفلية لزر التحميل */
        border-radius: 0; /* يمكنك إزالة الحواف الدائرية لتبدو متصلة بالشاشة */
    }
}

/* Creator Info Loading Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick Loading Indicator Styles */
#quick-loading-indicator {
    backdrop-filter: blur(2px);
}

#quick-loading-indicator div {
    background: transparent;
}

/* Error Message Animation */
@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Custom Sections Styles */
.custom-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 2px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.custom-section:hover {
    border-color: rgba(255, 215, 0, 0.4);
    box-shadow: none;
}

.custom-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.custom-section .section-title-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.custom-section .section-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.custom-section .section-icon i {
    color: #ffd700;
    font-size: 20px;
    /* text-shadow removed */
}

.custom-section .section-title {
    color: #ffd700;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
    /* text-shadow removed */
    font-family: 'Roboto', sans-serif; /* خط مميز مناسب */
    font-size: 1.6rem; /* حجم خط مناسب */
    letter-spacing: 0; /* إزالة تباعد الأحرف */
}

.custom-section .section-description {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 15px;
    font-style: italic;
    opacity: 0.8;
}

.custom-section .see-all-link {
    color: #ffd700;
    text-decoration: none;
    font-weight: bold;
    padding: 8px 16px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    transition: all 0.3s ease;
    background: rgba(255, 215, 0, 0.1);
    font-family: 'Roboto', sans-serif; /* خط مميز مناسب */
    font-size: 0.9rem; /* حجم خط مناسب */
    letter-spacing: 0; /* إزالة تباعد الأحرف */
}

.custom-section .see-all-link:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    transform: translateY(-2px);
    box-shadow: none;
}

/* Custom section view container */
#custom-section-view {
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    margin: 20px 0;
}

#custom-section-view .section-header {
    margin-bottom: 30px;
    text-align: center;
}

#custom-section-view .section-title-container {
    justify-content: center;
    margin-bottom: 15px;
}

#custom-section-view .section-icon {
    width: 60px;
    height: 60px;
}

#custom-section-view .section-icon i {
    font-size: 24px;
}

#custom-section-view .section-title {
    font-size: 2rem;
}

/* Animation for custom sections */
.custom-section {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive design for custom sections */
@media (max-width: 768px) {
    .custom-section .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .custom-section .section-title-container {
        gap: 10px;
    }

    .custom-section .section-icon {
        width: 40px;
        height: 40px;
    }

    .custom-section .section-icon i {
        font-size: 16px;
    }

    .custom-section .section-title {
        font-size: 1.3rem;
    }

    .custom-section .see-all-link {
        font-size: 0.7rem;
        padding: 6px 12px;
    }

    #custom-section-view .section-icon {
        width: 50px;
        height: 50px;
    }

    #custom-section-view .section-icon i {
        font-size: 20px;
    }

    #custom-section-view .section-title {
        font-size: 1.6rem;
    }
}

/* Style to hide sections with no content */
.hidden-section {
    display: none !important;
}
}
/* ======================================= */
/* === إضافة هامش داخلي لمحتوى النافذة === */
/* ======================================= */
.modal-inner-content {
    padding: 10px 15px; /* 15px من اليمين واليسار لمحاذاة الصورة الرئيسية */
    margin: 0; /* إزالة أي هوامش خارجية */
}

/* التأكد من أن جميع عناصر المحتوى محاذية */
.modal-inner-content h2,
.modal-inner-content p,
.modal-inner-content .mod-details,
.modal-inner-content .creator-info {
    margin-left: 0;
    margin-right: 0;
}

/* ضمان التناسق البصري الكامل - جميع العناصر تتبع نفس الهوامش */
.modal .main-image,
.modal .thumbnail-container,
.modal .modal-inner-content {
    /* جميع العناصر تحصل على نفس الهوامش الأفقية 15px */
    box-sizing: border-box;
}

/* تحسين إضافي للصورة الرئيسية */
.main-image:hover {
    transform: scale(1.03) !important; /* تأثير تكبير خفيف عند التمرير */
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.7), 0 6px 20px rgba(0, 0, 0, 0.25) !important; /* ظل ذهبي أقوى عند التمرير */
}

/* قاعدة إضافية للتأكد من أن الصورة الرئيسية تلتزم بالهوامش - أولوية قصوى */
#mainImage.main-image,
img.main-image,
.modal-content .main-image,
.modal .main-image {
    width: calc(100% - 30px) !important;
    max-width: calc(100% - 30px) !important;
    margin: 15px auto 1rem !important;
    display: block !important;
    border-radius: 15px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* حاوية الصورة الرئيسية - لا تؤثر على عرض الصورة */
.main-image-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    text-align: center !important;
    position: relative !important;
}

/* قاعدة شاملة نهائية - منع أي تداخل مع الصورة الرئيسية */
.modal-content img#mainImage,
.modal img#mainImage,
#modalContent img#mainImage {
    width: calc(100% - 30px) !important;
    max-width: calc(100% - 30px) !important;
    margin: 15px auto 1rem !important;
    display: block !important;
    border-radius: 15px !important;
    object-fit: cover !important;
    height: auto !important;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2) !important; /* ظل ذهبي */
    background-color: transparent !important;
    border: 3px solid #ffd700 !important; /* إطار ذهبي */
}

/* قاعدة خاصة بالصور المصغرة فقط - النمط الذهبي */
.thumbnail-container img {
    border: 3px solid #ffd700 !important; /* إطار ذهبي للصور المصغرة فقط */
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.4), 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    transition: transform 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* ======================================= */
/* === أنماط أزرار التحميل المباشرة === */
/* ======================================= */

/* Direct download button styles */
.direct-download-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 5;
}

.direct-download-btn {
    pointer-events: all;
    transition: all 0.3s ease;
    font-weight: bold;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.direct-download-btn:hover:not(:disabled) {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    border-color: rgba(255, 255, 255, 0.6);
}

.direct-download-btn:active:not(:disabled) {
    transform: scale(0.95);
}

.direct-download-btn.downloaded {
    background: #28a745 !important;
    border-color: rgba(255, 255, 255, 0.5);
}

.direct-download-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Progress ring animation */
.download-progress-ring {
    border-top-color: #ffc107 !important;
    border-right-color: #ffc107 !important;
    border-bottom-color: transparent !important;
    border-left-color: transparent !important;
}

/* تحسين تأثيرات الأزرار المباشرة */
.direct-download-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: -1;
}

.direct-download-btn:hover:not(:disabled)::before {
    width: 100%;
    height: 100%;
}

/* تأثير النبض للأزرار المحملة */
.direct-download-btn.downloaded {
    animation: downloadedPulse 2s infinite;
}

@keyframes downloadedPulse {
    0% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(40, 167, 69, 0.4);
    }
    100% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }
}

/* تحسين مظهر الأزرار على الشاشات الصغيرة */
@media (max-width: 768px) {
    .direct-download-btn {
        width: 35px;
        height: 35px;
    }

    .direct-download-btn i {
        font-size: 14px !important;
    }

    .download-progress-ring {
        width: 39px !important;
        height: 39px !important;
    }
}
