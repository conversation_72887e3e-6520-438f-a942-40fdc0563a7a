const drawer = document.querySelector(".drawer");
const drawerBtn = document.querySelector(".drawer-btn");
const overlay = document.querySelector(".drawer-overlay");

// The event listener for mod cards is now added directly within the createModElement function.
// This avoids duplicate event handling and ensures the correct `showModal` function is called.

// Global state for current view


let currentCategory = 'All';
let currentSortBy = 'created_at'; // Default sort column
let currentSortAscending = false; // Default sort order (false = descending/newest)
// Removed displayedModsData variable

// Function to get localized description based on user's language preference
function getLocalizedDescription(item) {
    const currentLanguage = localStorage.getItem('selectedLanguage') || 'en';
    const descriptionType = localStorage.getItem('descriptionType') || 'official';

    if (currentLanguage === 'ar') {
        // Check description type preference
        if (descriptionType === 'funny' && item.telegram_description_ar) {
            // Use funny telegram description if available
            return item.telegram_description_ar;
        } else if (descriptionType === 'funny' && item.telegram_description) {
            // Fallback to English telegram description
            return item.telegram_description;
        } else {
            // Use official description
            return item.description_ar || item.description || t('no_description');
        }
    } else {
        // English language
        if (descriptionType === 'funny' && item.telegram_description) {
            // Use funny telegram description
            return item.telegram_description;
        } else {
            // Use official description
            return item.description || t('no_description');
        }
    }
}

// Function to save user language preference to database
async function saveUserLanguagePreference(language) {
    try {
        // Use the global supabaseClient
        if (!supabaseClient) {
            console.warn('Supabase not available, skipping language save');
            return false;
        }

        // Get or generate user ID
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
            localStorage.setItem('userId', userId);
        }

        // Prepare device info
        const deviceInfo = {
            device: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
            browser: getBrowserName(),
            screen: `${screen.width}x${screen.height}`,
            language: navigator.language,
            timestamp: new Date().toISOString()
        };

        // Save to database
        const { error } = await supabaseClient
            .from('user_languages')
            .upsert([
                {
                    user_id: userId,
                    selected_language: language,
                    device_info: deviceInfo,
                    user_agent: navigator.userAgent,
                    updated_at: new Date().toISOString()
                }
            ], {
                onConflict: 'user_id'
            });

        if (error) {
            console.error('Error saving user language preference:', error);
            return false;
        } else {
            console.log('User language preference saved successfully');
            return true;
        }
    } catch (error) {
        console.error('Error in saveUserLanguagePreference:', error);
        return false;
    }
}

// Helper function to get browser name
function getBrowserName() {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'chrome';
    if (userAgent.includes('Firefox')) return 'firefox';
    if (userAgent.includes('Safari')) return 'safari';
    if (userAgent.includes('Edge')) return 'edge';
    return 'unknown';
}

// Function to show language selection modal
function showLanguageSelectionModal() {
    // Remove existing modal if any
    const existingModal = document.getElementById('language-selection-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'language-selection-modal';
    modalOverlay.className = 'language-selection-overlay';

    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'language-selection-container';

    // Create title
    const title = document.createElement('h2');
    title.textContent = t('choose_language');
    title.className = 'language-selection-title';

    // Create subtitle
    const subtitle = document.createElement('p');
    subtitle.textContent = t('language_subtitle');
    subtitle.className = 'language-selection-subtitle';

    // Create language options container
    const optionsContainer = document.createElement('div');
    optionsContainer.className = 'language-options';

    // Create Arabic option
    const arabicOption = document.createElement('button');
    arabicOption.innerHTML = '<span class="language-flag">🇸🇦</span>العربية';
    arabicOption.className = 'language-option';
    arabicOption.onclick = () => changeLanguage('ar');

    // Create English option
    const englishOption = document.createElement('button');
    englishOption.innerHTML = '<span class="language-flag">🇺🇸</span>English';
    englishOption.className = 'language-option';
    englishOption.onclick = () => changeLanguage('en');

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.textContent = '✕';
    closeButton.className = 'language-close-button';
    closeButton.onclick = () => modalOverlay.remove();

    // Add elements to containers
    optionsContainer.appendChild(arabicOption);
    optionsContainer.appendChild(englishOption);

    modalContainer.appendChild(closeButton);
    modalContainer.appendChild(title);
    modalContainer.appendChild(subtitle);
    modalContainer.appendChild(optionsContainer);

    modalOverlay.appendChild(modalContainer);
    
    // Force any existing body styles to allow full-width content
    document.documentElement.style.width = '100%';
    document.documentElement.style.maxWidth = '100%';
    document.documentElement.style.overflowX = 'hidden';
    document.body.style.width = '100%';
    document.body.style.maxWidth = '100%';
    document.body.style.overflowX = 'hidden';
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    
    // Add the modal to the body
    document.body.appendChild(modalOverlay);
    
    // Force the modal to take full width
    modalOverlay.style.width = '100vw';
    modalOverlay.style.maxWidth = '100vw';
    modalOverlay.style.left = '0';
    modalOverlay.style.right = '0';
    modalOverlay.style.margin = '0';
    modalOverlay.style.padding = '0';
    
    modalContainer.style.width = '100vw';
    modalContainer.style.maxWidth = '100vw';
    modalContainer.style.left = '0';
    modalContainer.style.right = '0';
    modalContainer.style.margin = '0';
    modalContainer.style.padding = '0';

    // Close modal when clicking outside
    modalOverlay.onclick = (e) => {
        if (e.target === modalOverlay) {
            modalOverlay.remove();
        }
    };
}

// Function to change language
async function changeLanguage(language) {
    try {
        // Update translation manager
        if (window.translationManager) {
            window.translationManager.setLanguage(language);
        }

        // Save to localStorage
        localStorage.setItem('selectedLanguage', language);
        localStorage.setItem('languageSelected', 'true');

        // Save to database
        await saveUserLanguagePreference(language);

        // Close modal
        const modal = document.getElementById('language-selection-modal');
        if (modal) {
            modal.remove();
        }

        // Show success message
        showLanguageChangeSuccess(language);

        // Reload page to apply language changes
        setTimeout(() => {
            location.reload();
        }, 1500);

    } catch (error) {
        console.error('Error changing language:', error);
        // Still reload even if saving fails
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

// Function to show language change success message
function showLanguageChangeSuccess(language) {
    const successMessage = document.createElement('div');
    const messageText = t('language_changed_success');

    successMessage.textContent = messageText;
    successMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, #4ade80, #22c55e);
        color: white;
        padding: 20px 30px;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: bold;
        z-index: 100011;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        animation: fadeIn 0.3s ease;
    `;

    document.body.appendChild(successMessage);

    // Remove message after 1.2 seconds
    setTimeout(() => {
        if (successMessage.parentNode) {
            successMessage.remove();
        }
    }, 1200);
}


// --- Function called by Kotlin after onPageFinished ---
// Moved function definition higher up to ensure it's defined when called
function attachDelayedEventListeners() {
    console.log(">>> attachDelayedEventListeners() called from Kotlin.");
    const categoriesContainer = document.getElementById("categories");

    if (categoriesContainer) {
        // Check if listener already attached to prevent duplicates if onPageFinished fires multiple times
        if (!categoriesContainer.dataset.listenerAttached) {
            console.log("Attaching delegated category click listener immediately.");
            // Attach listener immediately when called
            categoriesContainer.addEventListener("click", function(event) {
                // Check if the clicked element is a category button
                if (event.target.matches('.category-btn')) {
                    const category = event.target.getAttribute('data-category');
                    if (category) {
                        console.log(`>>> Category button clicked (delegated via onPageFinished): ${category}`);
                        filterItems(category); // Call filterItems directly

                        // Update active button styling
                        document.querySelectorAll('#categories .category-btn').forEach(btn => {
                            btn.classList.remove('active-category');
                        });
                        event.target.classList.add('active-category');
                    }
                }
            });
            categoriesContainer.dataset.listenerAttached = 'true'; // Mark as attached
        } else {
             console.log("Delegated category click listener already attached.");
        }
    } else {
        console.error("Categories container not found in attachDelayedEventListeners!");
    }
}


// --- UI: Drawer ---
// Function called by onclick attribute in HTML
function toggleDrawer() {
    console.log("toggleDrawer() function called."); // Added log
    if (drawer) drawer.classList.add("active");
    if (overlay) overlay.classList.add("active");
}

// Add null checks for elements that might not exist on all pages (like search.html)
// REMOVED: Direct event listener for drawerBtn as onclick is used in HTML
// if (drawerBtn) { ... }

if (overlay) {
    overlay.addEventListener("click", () => {
        if (drawer) drawer.classList.remove("active");
        overlay.classList.remove("active");
    });
} else {
    // console.log("Drawer overlay (.drawer-overlay) not found on this page."); // Optional log
}

if (drawer) {
    drawer.addEventListener("click", (event) => {
        if (event.target.tagName === "A") {
            console.log("Link clicked: " + event.target.href);
            // Keep drawer open or close based on preference
            // drawer.classList.remove("active");
            // overlay.classList.remove("active");
        }
    });
} else {
    // console.log("Drawer (.drawer) not found on this page."); // Optional log
}

// --- UI: Modal ---
function closeModal() {
    try {
        console.log("Attempting to close modal..."); // Debug log

        // إغلاق النافذة الرئيسية
        const modal = document.getElementById('modal'); // Reverted ID
        const bottomBar = document.querySelector('.bottom-fixed-bar'); // Get bottom bar

        if (modal) {
            console.log("Closing main modal."); // Debug log
            modal.style.display = 'none';
            modal.style.visibility = 'hidden';
            
            // إزالة الـ classes بقوة
            document.documentElement.classList.remove('modal-open');
            document.body.classList.remove('modal-open');
            
            // إعادة تعيين جميع الـ inline styles بقوة لضمان إزالة تأثيرات CSS
            document.documentElement.style.overflow = '';
            document.documentElement.style.position = '';
            document.documentElement.style.width = '';
            document.documentElement.style.maxWidth = '';
            document.documentElement.style.height = '';
            
            document.body.style.overflow = '';
            document.body.style.position = '';
            document.body.style.width = '';
            document.body.style.maxWidth = '';
            document.body.style.height = '';
            document.body.style.margin = '';
            document.body.style.padding = '';
            
            // إعادة تفعيل pointer events
            document.body.style.pointerEvents = '';
            document.documentElement.style.pointerEvents = '';
            
            if (bottomBar) bottomBar.classList.remove('hidden'); // Show bottom bar
        }

        // إغلاق أي نوافذ أخرى مفتوحة
        const creatorModal = document.querySelector('.creator-info-modal');
        if (creatorModal) {
            console.log("Closing creator info modal.");
            creatorModal.style.display = 'none';
            creatorModal.style.visibility = 'hidden';
        }

        const customDialog = document.querySelector('.custom-dialog-overlay');
        if (customDialog) {
            console.log("Closing custom dialog.");
            customDialog.style.display = 'none';
            customDialog.style.visibility = 'hidden';
        }

        const languageModal = document.getElementById('language-selection-modal');
        if (languageModal) {
            console.log("Removing language selection modal.");
            languageModal.remove();
        }

        // إغلاق أي نوافذ اشتراك مفتوحة
        const subscriptionModals = document.querySelectorAll('.subscription-modal, .premium-modal');
        subscriptionModals.forEach(subModal => {
            if (subModal) {
                console.log("Closing subscription modal.");
                subModal.style.display = 'none';
                subModal.style.visibility = 'hidden';
            }
        });

        // تنظيف إضافي: إزالة أي overlay متبقي
        const overlays = document.querySelectorAll('.modal-overlay, .overlay');
        overlays.forEach(overlay => {
            if (overlay && overlay.style.display !== 'none') {
                overlay.style.display = 'none';
                overlay.style.visibility = 'hidden';
            }
        });

        // تنظيف شامل نهائي لضمان إزالة جميع التأثيرات
        if (typeof forceCleanupModalEffects === 'function') {
            forceCleanupModalEffects();
        }

        console.log("Modal closing completed successfully.");
        return true;

    } catch (error) {
        console.error("Error closing modal:", error);
        return false;
    }
}

// Close modal if clicking outside the content
window.onclick = (e) => {
    const modal = document.getElementById('modal'); // Reverted ID
    if (modal && e.target === modal) { // Check if modal exists before comparing
        console.log("Clicked outside modal content."); // Debug log
        closeModal();
    }
};

// وظيفة للتحقق من وجود نوافذ مفتوحة (تستخدم من Android)
function hasOpenModals() {
    try {
        const modal = document.getElementById('modal');
        if (modal && (modal.style.display === 'flex' || modal.style.display === 'block' || window.getComputedStyle(modal).display !== 'none')) {
            return true;
        }

        const creatorModal = document.querySelector('.creator-info-modal');
        if (creatorModal && window.getComputedStyle(creatorModal).display !== 'none') {
            return true;
        }

        const customDialog = document.querySelector('.custom-dialog-overlay');
        if (customDialog && window.getComputedStyle(customDialog).display !== 'none') {
            return true;
        }

        const languageModal = document.getElementById('language-selection-modal');
        if (languageModal && window.getComputedStyle(languageModal).display !== 'none') {
            return true;
        }

        const subscriptionModal = document.querySelector('.subscription-modal, .premium-modal');
        if (subscriptionModal && window.getComputedStyle(subscriptionModal).display !== 'none') {
            return true;
        }

        return false;
    } catch (error) {
        console.error('Error checking for open modals:', error);
        return false;
    }
}

// دالة تنظيف شاملة لإزالة جميع تأثيرات المودال
function forceCleanupModalEffects() {
    try {
        console.log("Force cleaning up modal effects...");
        
        // إزالة جميع الـ classes
        document.documentElement.classList.remove('modal-open');
        document.body.classList.remove('modal-open');
        
        // إعادة تعيين جميع الـ styles المحتملة
        const elementsToClean = [document.documentElement, document.body];
        
        elementsToClean.forEach(element => {
            element.style.overflow = '';
            element.style.position = '';
            element.style.width = '';
            element.style.maxWidth = '';
            element.style.height = '';
            element.style.margin = '';
            element.style.padding = '';
            element.style.pointerEvents = '';
        });
        
        // إخفاء جميع المودالات المحتملة
        const modalSelectors = [
            '#modal',
            '.creator-info-modal',
            '.custom-dialog-overlay',
            '.subscription-modal',
            '.premium-modal',
            '.modal-overlay',
            '.overlay'
        ];
        
        modalSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.style.display = 'none';
                element.style.visibility = 'hidden';
            });
        });
        
        console.log("Force cleanup completed successfully.");
        return true;
        
    } catch (error) {
        console.error("Error in force cleanup:", error);
        return false;
    }
}

// معالجة زر الرجوع في المتصفح
window.addEventListener('popstate', function(event) {
    console.log('Browser back button pressed');
    if (hasOpenModals()) {
        console.log('Modal detected, closing instead of navigating back');
        closeModal();
        // تنظيف إضافي للتأكد
        setTimeout(() => {
            forceCleanupModalEffects();
        }, 100);
        // منع التنقل للخلف
        history.pushState(null, null, window.location.href);
    }
});

// إضافة حالة للتاريخ عند فتح نافذة
function pushModalState() {
    if (hasOpenModals()) {
        history.pushState({modalOpen: true}, null, window.location.href);
    }
}

// --- Navigation ---
function navigateToSearchPage() {
    console.log("Navigate to search page called."); // Added log
    window.location.href = 'search.html'; // Assuming search.html exists
}

// --- Supabase Configuration (Centralized) ---
// Configuration moved to supabase-manager.js to prevent multiple client instances

// Enhanced function to check network connectivity with multiple fallbacks
async function checkNetworkConnectivity() {
    // Use the network handler if available
    if (typeof networkHandler !== 'undefined' && networkHandler.checkNetworkConnectivity) {
        return await networkHandler.checkNetworkConnectivity();
    }

    // Fallback to original implementation
    const testUrls = [
        'https://www.google.com/favicon.ico',
        'https://httpbin.org/status/200',
        'https://jsonplaceholder.typicode.com/posts/1'
    ];

    for (const url of testUrls) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(url, {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache',
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            return true;
        } catch (error) {
            console.warn(`Network check failed with ${url}:`, error.message);
            continue;
        }
    }

    console.error('All network connectivity checks failed');
    return false;
}

// Function to show network error message
function showNetworkError() {
    const errorContainer = document.createElement('div');
    errorContainer.id = 'network-error-message';
    errorContainer.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        z-index: 10000;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 90%;
    `;
    errorContainer.innerHTML = `
        <h3 style="margin-bottom: 10px;">مشكلة في الاتصال</h3>
        <p style="margin-bottom: 15px;">تعذر الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.</p>
        <button onclick="location.reload()" style="
            background: white;
            color: #ee5a24;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
        ">إعادة المحاولة</button>
    `;

    // Remove existing error message if any
    const existingError = document.getElementById('network-error-message');
    if (existingError) {
        existingError.remove();
    }

    document.body.appendChild(errorContainer);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (errorContainer.parentNode) {
            errorContainer.remove();
        }
    }, 10000);
}

// Get Supabase client from centralized manager
let supabaseClient;
let tableNames;

// Initialize client and table names
async function initializeSupabase() {
    try {
        // انتظار supabaseManager إذا لم يكن متاحاً
        if (typeof window.supabaseManager === 'undefined' || !window.supabaseManager.getMainClient) {
            if (window.supabaseManagerFix && window.supabaseManagerFix.waitForSupabaseManager) {
                try {
                    await window.supabaseManagerFix.waitForSupabaseManager(10000);
                } catch (waitError) {
                    console.warn('⚠️ فشل في انتظار supabaseManager في script.js:', waitError);
                }
            }
        }

        if (typeof window.supabaseManager !== 'undefined' && window.supabaseManager.getClient) {
            supabaseClient = window.supabaseManager.getClient();
            console.log('✅ Using simple Supabase manager client');
        } else {
            console.warn('⚠️ Supabase manager not available, falling back to direct client');

            // التحقق من وجود مكتبة Supabase
            if (typeof window.supabase === 'undefined') {
                console.error('❌ Supabase library not loaded');
                return;
            }

            // Fallback for backward compatibility
            const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
            supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
            tableNames = {
                MODS: 'mods',
                SUGGESTED_MODS: 'suggested_mods',
                FEATURED_ADDONS: 'featured_addons',
                FREE_ADDONS: 'free_addons',
                BANNER_ADS: 'banner_ads',
                UPDATE_NOTIFICATIONS: 'update_notifications',
                APP_ANNOUNCEMENTS: 'app_announcements',
                DRAWER_LINKS: 'drawer_links',
                ENTRY_SUBSCRIPTION_ADS: 'entry_subscription_ads' // Added new table name
            };
        }

        // جع اعمي متاحاً عامياً
        window.supabaseClient = supabaseClient;

        console.log('✅ Supabase initialized successfully');
    } catch (error) {
        console.error('❌ خطأ في تهيئة Supabase في script.js:', error);
    }
}

// Legacy table constants for backward compatibility
const UPDATE_NOTIFICATIONS_TABLE = 'update_notifications';
const APP_ANNOUNCEMENTS_TABLE = 'app_announcements';
const DRAWER_LINKS_TABLE = 'drawer_links';
const SUGGESTED_MODS_TABLE = 'suggested_mods';
const FEATURED_ADDONS_TABLE = 'featured_addons';

// Initialize app with update check
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize Supabase client first
    await initializeSupabase();

    // Check network connectivity first
    setTimeout(async () => {
        const isConnected = await checkNetworkConnectivity();
        if (!isConnected) {
            console.error('No network connectivity detected on app start');
            showNetworkError();
            return;
        }

        // Check for update notifications after a short delay
        fetchAndDisplayUpdateNotification();

        // Initialize custom sections system
        setTimeout(async () => {
            if (window.customSectionsManager) {
                await window.customSectionsManager.initializeCustomSections();
            }
        }, 1000); // 1 second delay

        // Initialize floating subscription icon
        setTimeout(() => {
            initializeFloatingSubscriptionIcon();
        }, 2500); // 2.5 seconds delay

        // Initialize subscription banners
        setTimeout(() => {
            initializeSubscriptionBanners();
        }, 3000); // 3 seconds delay

        // Check for entry subscription ads after app loads
        setTimeout(() => {
            checkAndShowEntrySubscriptionAd();
        }, 3500); // 3.5 seconds delay after app loads

        // Check for auto download after subscription
        setTimeout(() => {
            checkAutoDownload();
        }, 4000); // 4 seconds delay to ensure app is fully loaded
    }, 2000); // 2 seconds delay to allow app to load first
});

// Function to check and show entry subscription ad based on settings
async function checkAndShowEntrySubscriptionAd() {
    try {
        // Check if entry subscription ads should be shown
        const shouldShowOnEntry = localStorage.getItem('showSubscriptionOnEntry') === 'true';

        if (shouldShowOnEntry) {
            await fetchAndDisplayEntrySubscriptionAd();
        }
    } catch (error) {
        console.error('Error checking entry subscription ad settings:', error);
    }
}

// Function to check if user has active subscription
async function checkActiveSubscription() {
    try {
        // اتحقق من وجود عمي Supabase
        if (!supabaseClient) {
            console.warn('Supabase client not available for subscription check');
            return false;
        }

        const userId = generateUserId();

        // اتحقق من صحة معرف امستخدم
        if (!userId) {
            console.warn('User ID not available for subscription check');
            return false;
        }

        // محاوة فحص ااشتراك مع معاجة أفض أخطاء
        const { data: subscriptions, error } = await supabaseClient
            .from('user_subscriptions')
            .select('id, status, expires_at, created_at')
            .eq('user_id', userId)
            .eq('status', 'active')
            .gte('expires_at', new Date().toISOString())
            .order('created_at', { ascending: false });

        if (error) {
            // معاجة أخطاء مختفة
            if (error.code === 'PGRST116') {
                // ا توجد صفوف - هذا طبيعي
                console.log('No active subscriptions found for user');
                return false;
            } else if (error.code === '406' || error.message.includes('Not Acceptable')) {
                // خطأ 406 - مشكة في تنسيق اطب
                console.warn('Subscription check failed due to request format issue:', error);

                // محاوة استعام مبسط
                try {
                    const { data: simpleCheck, error: simpleError } = await supabaseClient
                        .from('user_subscriptions')
                        .select('id')
                        .eq('user_id', userId)
                        .limit(1);

                    if (simpleError) {
                        console.error('Simple subscription check also failed:', simpleError);
                        return false;
                    }

                    return simpleCheck && simpleCheck.length > 0;
                } catch (simpleCheckError) {
                    console.error('Simple subscription check error:', simpleCheckError);
                    return false;
                }
            } else {
                console.error('Error checking active subscription:', error);
                return false;
            }
        }

        // اتحقق من انتائج
        if (!subscriptions || subscriptions.length === 0) {
            console.log('No active subscriptions found');
            return false;
        }

        // اتحقق من صحة تاريخ انتهاء اصاحية
        const activeSubscription = subscriptions.find(sub => {
            const expiresAt = new Date(sub.expires_at);
            const now = new Date();
            return expiresAt > now;
        });

        if (activeSubscription) {
            console.log('Active subscription found:', activeSubscription.id);
            return true;
        } else {
            console.log('No valid active subscriptions found');
            return false;
        }

    } catch (error) {
        console.error('Unexpected error in checkActiveSubscription:', error);

        // في حاة اخطأ، اتحقق من اتخزين امحي كبدي
        try {
            const localSubscription = localStorage.getItem('hasActiveSubscription');
            if (localSubscription === 'true') {
                const subscriptionExpiry = localStorage.getItem('subscriptionExpiry');
                if (subscriptionExpiry && new Date(subscriptionExpiry) > new Date()) {
                    console.log('Using local subscription data as fallback');
                    return true;
                }
            }
        } catch (localError) {
            console.warn('Local subscription check failed:', localError);
        }

        return false;
    }
}


// Function to check for auto download after subscription
function checkAutoDownload() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const autoDownloadModId = urlParams.get('autoDownload');

        if (autoDownloadModId) {
            console.log('Auto download requested for mod:', autoDownloadModId);

            // Clear the URL parameter
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);

            // Wait for mods to load then trigger download
            setTimeout(() => {
                triggerAutoDownload(autoDownloadModId);
            }, 2000);
        }
    } catch (error) {
        console.error('Error checking auto download:', error);
    }
}

// Function to trigger auto download for a specific mod
async function triggerAutoDownload(modId) {
    try {
        // Find the mod in the loaded data
        const allMods = [...(window.allModsData || [])];
        const targetMod = allMods.find(mod => mod.id.toString() === modId.toString());

        if (targetMod) {
            console.log('Found mod for auto download:', targetMod.name);

            // Show the mod details modal first
            showModDetails(targetMod);

            // Wait a bit then trigger download
            setTimeout(() => {
                const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
                if (downloadButton) {
                    downloadButton.click();
                    console.log('Auto download triggered for mod:', targetMod.name);
                } else {
                    console.warn('Download button not found for auto download');
                }
            }, 1000);
        } else {
            console.warn('Mod not found for auto download:', modId);
        }
    } catch (error) {
        console.error('Error triggering auto download:', error);
    }
}

// Floating Subscription Icon Logic
async function initializeFloatingSubscriptionIcon() {
    console.log("Initializing floating subscription icon...");

    // ابحث عن اعناصر بطرق مختفة
    let floatingIcon = document.getElementById('floatingSubscriptionIcon') ||
                      document.getElementById('floating-subscription-icon') ||
                      document.querySelector('.floating-subscription-icon');

    let floatingIconImage = document.getElementById('floatingIconImage') ||
                           document.getElementById('floating-subscription-icon-image') ||
                           document.querySelector('.floating-icon-image');

    // إذا م توجد اعناصر، محاوة إنشاؤها
    if (!floatingIcon || !floatingIconImage) {
        console.log("Floating subscription icon elements not found, attempting to create them...");

        try {
            // إنشاء اعنصر ارئيسي
            if (!floatingIcon) {
                floatingIcon = document.createElement('div');
                floatingIcon.id = 'floatingSubscriptionIcon';
                floatingIcon.className = 'floating-subscription-icon';
                floatingIcon.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
                    cursor: pointer;
                    z-index: 1000;
                    display: none;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;
                    animation: pulse 2s infinite;
                `;

                // إضافة تأثيرات CSS
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes pulse {
                        0% { transform: scale(1); }
                        50% { transform: scale(1.05); }
                        100% { transform: scale(1); }
                    }
                    .floating-subscription-icon:hover {
                        transform: scale(1.1) !important;
                        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
                    }
                `;
                document.head.appendChild(style);

                document.body.appendChild(floatingIcon);
            }

            // إنشاء عنصر اصورة
            if (!floatingIconImage) {
                floatingIconImage = document.createElement('img');
                floatingIconImage.id = 'floatingIconImage';
                floatingIconImage.className = 'floating-icon-image';
                floatingIconImage.style.cssText = `
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    object-fit: cover;
                `;
                floatingIcon.appendChild(floatingIconImage);
            }

            console.log("✅ Floating subscription icon elements created successfully");

        } catch (creationError) {
            console.error("❌ Failed to create floating subscription icon elements:", creationError);
            return;
        }
    }

    try {
        // Check if floating icon is enabled in localStorage
        const enableFloatingIcon = localStorage.getItem('enableFloatingIcon') === 'true';
        const floatingIconImageUrl = localStorage.getItem('floatingIconImageUrl') || '';
        const defaultCampaign = localStorage.getItem('defaultSubscriptionCampaign') || '';

        if (!enableFloatingIcon) {
            console.log("Floating icon is disabled in settings.");
            floatingIcon.style.display = 'none';
            return;
        }

        if (!floatingIconImageUrl) {
            console.warn("Floating icon image URL is missing in settings.");
            floatingIcon.style.display = 'none';
            return;
        }

        // Check if user has an active subscription
        const hasActiveSubscription = await checkActiveSubscription();
        if (hasActiveSubscription) {
            console.log("User has an active subscription, hiding floating icon.");
            floatingIcon.style.display = 'none';
            return;
        }

        // Set image source
        floatingIconImage.src = floatingIconImageUrl;
        floatingIconImage.onerror = () => {
            console.warn("Failed to load floating icon image, hiding icon.");
            floatingIcon.style.display = 'none';
        };

        // Show icon after a delay and trigger animation
        setTimeout(() => {
            floatingIcon.style.display = 'flex'; // Make it visible
            floatingIcon.style.opacity = '1'; // Start animation
            floatingIcon.style.transform = 'translateY(-50%) translateX(0)'; // End animation
        }, 2000); // Matches CSS animation-delay

        // Add click listener
        floatingIcon.addEventListener('click', () => {
            console.log("Floating icon clicked, showing subscription.");
            handleFloatingIconClick(defaultCampaign);
        });

        console.log("Floating subscription icon initialized successfully.");

    } catch (error) {
        console.error("Error initializing floating subscription icon:", error);
        floatingIcon.style.display = 'none'; // Ensure it's hidden on error
    }
}

// Handle floating icon click
function handleFloatingIconClick(campaignId) {
    const useSubscriptionPage = localStorage.getItem('useSubscriptionPage') === 'true';

    if (useSubscriptionPage && campaignId) {
        // Redirect to subscription page
        window.location.href = `subscription-page.html?campaign=${campaignId}`;
    } else if (campaignId) {
        // Show subscription campaign modal
        showSubscriptionCampaignModal(campaignId);
    } else {
        // Get first active campaign
        getFirstActiveCampaign().then(campaign => {
            if (campaign) {
                if (useSubscriptionPage) {
                    window.location.href = `subscription-page.html?campaign=${campaign.id}`;
                } else {
                    showSubscriptionCampaignModal(campaign.id);
                }
            } else {
                showMessage('ا توجد حمات اشتراك نشطة حاياً', 'info');
            }
        });
    }
}

// Show subscription modal before download with skip option
async function showSubscriptionBeforeDownloadModal(campaign, modId, modName, downloadLink) {
    // Check if a modal is already open
    if (document.querySelector('.subscription-before-download-modal')) {
        console.log('Subscription before download modal is already open.');
        return;
    }

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'subscription-before-download-modal';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100020;
        animation: fadeIn 0.3s ease;
    `;

    // Determine language for display
    const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
    const isArabic = userLanguage === 'ar';

    const title = isArabic ? (campaign.title_ar || campaign.title || 'احص عى اشتراك مجاني!') : (campaign.title_en || campaign.title || 'Get Free Subscription!');
    const description = isArabic ? (campaign.description_ar || campaign.description || 'أكم امهام ابسيطة واحص عى تحمي بدون إعانات!') : (campaign.description_en || campaign.description || 'Complete simple tasks and get ad-free downloads!');
    const subscribeText = isArabic ? 'احص عى ااشتراك امجاني' : 'Get Free Subscription';
    const skipText = isArabic ? 'تخطي ومتابعة اتحمي' : 'Skip & Continue Download';

    // Create modal content
    modalOverlay.innerHTML = `
        <div class="subscription-modal-content" style="
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 30px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            text-align: center;
            border: 2px solid #ffd700;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            position: relative;
            animation: slideInUp 0.4s ease;
        ">
            <button class="close-subscription-modal" style="
                position: absolute;
                top: 15px;
                right: 15px;
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                z-index: 10001;
            ">&times;</button>

            ${campaign.popup_image_url ? `
                <img src="${campaign.popup_image_url}" style="
                    width: 100%;
                    max-width: 200px;
                    border-radius: 15px;
                    margin-bottom: 20px;
                    border: 2px solid #ffd700;
                ">
            ` : ''}

            <div style="
                font-size: 3rem;
                color: #ffd700;
                margin-bottom: 15px;
            ">🎁</div>

            <h2 style="
                color: #ffd700;
                margin-bottom: 15px;
                font-size: 1.8rem;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            ">${title}</h2>

            <p style="
                color: white;
                margin-bottom: 20px;
                line-height: 1.6;
                font-size: 1.1rem;
            ">${description}</p>

            <div style="
                background: rgba(255, 215, 0, 0.1);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 25px;
                border: 1px solid rgba(255, 215, 0, 0.3);
            ">
                <h3 style="color: #ffd700; margin-bottom: 10px;">🚀 مميزات ااشتراك امجاني:</h3>
                <ul style="color: white; text-align: ${isArabic ? 'right' : 'left'}; list-style: none; padding: 0;">
                    <li style="margin-bottom: 8px;">✅ تحمي بدون إعانات</li>
                    <li style="margin-bottom: 8px;">✅ سرعة تحمي فائقة</li>
                    <li style="margin-bottom: 8px;">✅ وصو مبكر مودات اجديدة</li>
                    <li style="margin-bottom: 8px;">✅ محتوى حصري مشتركين</li>
                </ul>
            </div>

            <div style="
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-top: 20px;
            ">
                <button class="subscribe-btn" style="
                    background: linear-gradient(45deg, #28a745, #20c997);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 25px;
                    font-weight: bold;
                    cursor: pointer;
                    font-size: 1.1rem;
                    transition: all 0.3s ease;
                    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
                ">
                    ${subscribeText}
                </button>

                <button class="skip-btn" style="
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    padding: 12px 25px;
                    border-radius: 20px;
                    font-weight: bold;
                    cursor: pointer;
                    font-size: 1rem;
                    transition: all 0.3s ease;
                ">
                    ${skipText}
                </button>
            </div>
        </div>
    `;

    // Add event listeners
    const subscribeBtn = modalOverlay.querySelector('.subscribe-btn');
    const skipBtn = modalOverlay.querySelector('.skip-btn');
    const closeBtn = modalOverlay.querySelector('.close-subscription-modal');

    subscribeBtn.addEventListener('click', () => {
        modalOverlay.remove();
        const useSubscriptionPage = localStorage.getItem('useSubscriptionPage') === 'true';

        if (useSubscriptionPage) {
            // Close current modal and redirect to subscription page
            closeModal();
            window.location.href = `subscription-page.html?campaign=${campaign.id}&return=download`;
        } else {
            // Show subscription campaign modal
            showSubscriptionCampaignModal(campaign.id);
        }
    });

    skipBtn.addEventListener('click', () => {
        modalOverlay.remove();
        // Continue with download
        console.log('User skipped subscription, continuing with download...');
        proceedWithDownload(modId, modName, downloadLink);
    });

    closeBtn.addEventListener('click', () => {
        modalOverlay.remove();
    });

    // Close modal when clicking outside content
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            modalOverlay.remove();
        }
    });

    // Add to document
    document.body.appendChild(modalOverlay);
}

// Proceed with download after skipping subscription
function proceedWithDownload(modId, modName, downloadLink) {
    // Continue with the normal download process
    console.log(`Proceeding with download for mod: ${modName}`);

    // Find UI elements for progress
    const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
    const downloadButtonText = downloadButton?.querySelector('.download-btn-text');

    // Show and initialize progress UI
    if (progressContainer) progressContainer.style.display = 'flex';
    if (progressBar) progressBar.style.width = '0%';
    if (progressText) progressText.textContent = '0%';
    if (downloadButtonText) downloadButtonText.style.display = 'none';

    // Start download
    downloadModFileWithFallback(modId, modName, downloadLink);
}

// Initialize subscription banners system
async function initializeSubscriptionBanners() {
    console.log("Initializing subscription banners...");

    try {
        // Check if subscription banners are enabled
        const enableBanners = localStorage.getItem('enableSubscriptionBanners') === 'true';

        if (!enableBanners) {
            console.log("Subscription banners are disabled in settings.");
            return;
        }

        // Check if user has active subscription
        const hasActiveSubscription = await checkActiveSubscription();
        if (hasActiveSubscription) {
            console.log("User has an active subscription, hiding subscription banners.");
            return;
        }

        // Fetch and display banner ads
        bannerAds = await fetchBannerAds();
        if (bannerAds && bannerAds.length > 0) {
            displayBannerAds();
            console.log(`Displayed ${bannerAds.length} subscription banners.`);
        } else {
            console.log("No subscription banners found to display.");
        }

    } catch (error) {
        console.error("Error initializing subscription banners:", error);
    }
}

// Get first active campaign
async function getFirstActiveCampaign() {
    try {
        if (!supabaseClient) {
            console.warn('Supabase client not initialized');
            return null;
        }

        const { data, error } = await supabaseClient
            .from('subscription_campaigns')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false })
            .limit(1);

        if (error) {
            console.error('Error fetching first active campaign:', error);
            return null;
        }

        return data && data.length > 0 ? data[0] : null;
    } catch (error) {
        console.error('Error getting first active campaign:', error);
        return null;
    }
}

// Enhanced banner ads display with subscription support
function displayBannerAds() {
    if (!bannerAds || bannerAds.length === 0) {
        console.log('No banner ads to display');
        return;
    }

    // Create banner container if it doesn't exist
    let bannerContainer = document.getElementById('banner-ad-container');
    if (!bannerContainer) {
        bannerContainer = document.createElement('div');
        bannerContainer.id = 'banner-ad-container';
        bannerContainer.className = 'banner-ad-container';

        // Create a wrapper div for better spacing
        const bannerWrapper = document.createElement('div');
        bannerWrapper.id = 'banner-ad-wrapper';
        bannerWrapper.style.cssText = 'padding: 10px 0; width: 100%;';
        bannerWrapper.appendChild(bannerContainer);

        // Insert before news section
        const newsSection = document.getElementById('news-section');
        if (newsSection && newsSection.parentNode) {
            newsSection.parentNode.insertBefore(bannerWrapper, newsSection);
        } else {
            // Fallback: insert at the beginning of main content
            const mainContent = document.getElementById('main-content-wrapper');
            if (mainContent) {
                mainContent.insertBefore(bannerWrapper, mainContent.firstChild);
            } else {
                console.error('Could not find suitable location to insert banner');
                return;
            }
        }
    }

    // Clear existing content
    bannerContainer.innerHTML = '';

    // Create slides for each banner
    bannerAds.forEach((ad, index) => {
        // Increment view count for each displayed banner
        const userId = generateUserId();
        const viewCountKey = `banner_view_count_${userId}_${ad.id}`;
        let currentViews = parseInt(localStorage.getItem(viewCountKey) || '0', 10);
        localStorage.setItem(viewCountKey, (currentViews + 1).toString());
        console.log(`Incremented view count for banner ad ${ad.id} to ${currentViews + 1}`);

        const slide = document.createElement('div');
        slide.className = `banner-ad-slide ${index === 0 ? 'active' : ''}`;
        slide.setAttribute('data-ad-id', ad.id);

        const img = document.createElement('img');
        img.className = 'banner-ad-image';
        img.src = ad.image_url;
        img.alt = ad.title || 'Banner Advertisement';
        img.onclick = () => handleBannerClick(ad);

        // Add subscription badge for subscription banners
        if (ad.banner_type === 'subscription') {
            const subscriptionBadge = document.createElement('div');
            subscriptionBadge.className = 'subscription-banner-badge';
            subscriptionBadge.innerHTML = '🎁 اشتراك مجاني';
            subscriptionBadge.style.cssText = `
                position: absolute;
                top: 10px;
                left: 10px;
                background: linear-gradient(45deg, #ffd700, #ffcc00);
                color: black;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: bold;
                z-index: 3;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            `;
            slide.appendChild(subscriptionBadge);
        }

        // Add title overlay if title exists
        if (ad.title) {
            const titleOverlay = document.createElement('div');
            titleOverlay.className = 'banner-ad-title-overlay';
            titleOverlay.textContent = ad.title;
            titleOverlay.style.cssText = `
                position: absolute;
                bottom: 10px;
                left: 10px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                z-index: 2;
            `;
            slide.appendChild(titleOverlay);
        }

        slide.appendChild(img);
        bannerContainer.appendChild(slide);
    });

    // Start rotation if more than one banner
    if (bannerAds.length > 1) {
        startBannerRotation();
    }
}

// --- Cache Configuration ---
const CACHE_DURATION_MS = 60 * 60 * 1000; // 1 hour cache validity

// --- Helper Functions ---
// Function to shuffle an array (Fisher-Yates shuffle)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

function generateUserId() {
    let userId = localStorage.getItem('userId');
    if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        localStorage.setItem('userId', userId);
    }
    return userId;
}

// Unified function to format large numbers (Likes, Downloads, etc.)
function formatCount(count) {
    const num = Number(count); // Ensure it's a number
    if (isNaN(num)) return '0'; // Return '0' if not a valid number

    if (num >= 1_000_000) return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + "M";
    if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, '') + "K";
    return num.toString();
}

// Helper function to check if a mod is recent (uses admin settings)
function isRecentMod(createdAt) {
    if (!createdAt) return false;

    try {
        const createdDate = new Date(createdAt);
        const now = new Date();
        const diffTime = Math.abs(now - createdDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Get duration from admin settings (default: 7 days)
        const adminDuration = parseInt(localStorage.getItem('newModsDuration') || '7');
        return diffDays <= adminDuration;
    } catch (e) {
        console.error("Error checking if mod is recent:", e);
        return false;
    }
}

// --- UI Update Helper ---
// Updates the like or download count in the UI for a specific mod
function updateCountUI(modId, type, count) {
    const formattedCount = formatCount(count);
    // This selector targets elements with class e.g. "download-count" AND the attribute data-mod-id="<modId>"
    // It should find the count spans in both the cards (list/grid) and the modal footer button.
    const selector = `.${type}-count[data-mod-id="${modId}"]`;

    console.log(`Updating UI elements matching selector: ${selector} to count: ${formattedCount}`); // Debug log

    const elementsToUpdate = document.querySelectorAll(selector);

    if (elementsToUpdate.length === 0) {
        console.warn(`No UI elements found for selector: ${selector}`);
    } else {
        elementsToUpdate.forEach(elem => {
            console.log(`Updating element:`, elem); // Debug log
            elem.textContent = formattedCount;
        });
    }

    // No separate modal logic needed here, as the querySelectorAll should cover it.
}

// --- Banner Ad Variables ---
const BANNER_ADS_TABLE = 'banner_ads';
let bannerAds = [];
let currentBannerIndex = 0;
let bannerInterval = null;

// --- Banner Ad Functions ---
async function fetchBannerAds() {
    const cacheKey = 'banner_ads_v2'; // Updated cache key for new logic
    const cachedData = localStorage.getItem(cacheKey);
    const userId = generateUserId(); // Get user ID for tracking

    if (cachedData) {
        try {
            const { timestamp, data } = JSON.parse(cachedData);
            if (Date.now() - timestamp < CACHE_DURATION_MS) {
                console.log(`Using cached banner ads data (v2)`);
                // Filter cached data based on display_type and display_limit_per_user
                const filteredData = data.filter(ad => {
                    if (ad.display_type !== 'banner') return false; // Only 'banner' type for this function
                    const viewCountKey = `banner_view_count_${userId}_${ad.id}`;
                    const currentViews = parseInt(localStorage.getItem(viewCountKey) || '0', 10);
                    return ad.display_limit_per_user === null || currentViews < ad.display_limit_per_user;
                });
                return filteredData;
            } else {
                console.log(`Cache expired for banner ads (v2)`);
                localStorage.removeItem(cacheKey);
            }
        } catch (e) {
            console.error("Error parsing cached banner ads data (v2):", e);
            localStorage.removeItem(cacheKey);
        }
    }

    console.log(`Fetching fresh banner ads from Supabase (v2)`);
    try {
        const { data, error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error fetching banner ads:', error);
            return null;
        }

        if (!data || data.length === 0) {
            console.log('No banner ads found.');
            return [];
        }

        // Cache all active banner ads (including subscription banners for later filtering)
        try {
            const dataToCache = { timestamp: Date.now(), data: data };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Banner ads data (v2) cached`);
        } catch (e) {
            console.error("Error storing banner ads data in localStorage (v2):", e);
        }

        // Filter fetched data based on display_type and display_limit_per_user
        const filteredData = data.filter(ad => {
            if (ad.display_type !== 'banner') return false; // Only 'banner' type for this function
            const viewCountKey = `banner_view_count_${userId}_${ad.id}`;
            const currentViews = parseInt(localStorage.getItem(viewCountKey) || '0', 10);
            return ad.display_limit_per_user === null || currentViews < ad.display_limit_per_user;
        });

        return filteredData;
    } catch (error) {
        console.error('Unexpected error in fetchBannerAds:', error);
        return null;
    }
}


function startBannerRotation() {
    // Clear any existing interval
    if (bannerInterval) {
        clearInterval(bannerInterval);
    }

    // Set interval to rotate banners every 5 seconds
    bannerInterval = setInterval(() => {
        const slides = document.querySelectorAll('.banner-ad-slide');
        if (slides.length <= 1) return;

        // Hide current slide
        slides[currentBannerIndex].classList.remove('active');

        // Move to next slide
        currentBannerIndex = (currentBannerIndex + 1) % slides.length;

        // Show new slide
        slides[currentBannerIndex].classList.add('active');
    }, 5000); // 5 seconds
}

// Banner click handler - Updated for all banner types including mod banners
function handleBannerClick(ad) {
    console.log('🖼️ Banner clicked:', ad);

    // Handle different banner types
    if (ad.banner_type === 'mod' && ad.target_mod_id) {
        // Handle mod banner - show mod details directly
        handleModBannerClick(ad);
    } else if (ad.banner_type === 'subscription' && ad.campaign_id) {
        // Handle subscription banner
        handleSubscriptionBannerClick(ad);
    } else {
        // Handle regular banner - show banner modal or redirect
        handleRegularBannerClick(ad);
    }
}

// Handle mod banner click
async function handleModBannerClick(ad) {
    try {
        console.log('🎮 Mod banner clicked, loading mod:', ad.target_mod_id);

        // Find the mod in allMods array first
        let targetMod = allMods.find(mod => mod.id === ad.target_mod_id);

        // If not found in allMods, fetch from database
        if (!targetMod) {
            const { data: mod, error } = await supabaseClient
                .from('mods')
                .select('*')
                .eq('id', ad.target_mod_id)
                .single();

            if (error) {
                console.error('❌ Error fetching mod:', error);
                showMessage('فش في تحمي تفاصي امود', 'error');
                return;
            }

            targetMod = mod;
        }

        if (targetMod) {
            // Show mod details modal directly
            console.log('✅ Showing mod details for:', targetMod.title);
            showModal(targetMod);
        } else {
            console.error('❌ Mod not found:', ad.target_mod_id);
            showMessage('امود امطوب غير موجود', 'error');
        }

    } catch (error) {
        console.error('❌ Error handling mod banner click:', error);
        showMessage('حدث خطأ أثناء تحمي تفاصي امود', 'error');
    }
}

// Handle subscription banner click
function handleSubscriptionBannerClick(ad) {
    // Check if should redirect to subscription page
    const useSubscriptionPage = localStorage.getItem('useSubscriptionPage') === 'true';

    if (useSubscriptionPage) {
        // Redirect to subscription page
        window.location.href = `subscription-page.html?campaign=${ad.campaign_id}`;
    } else {
        // Show subscription campaign modal (old behavior)
        showSubscriptionCampaignModal(ad.campaign_id);
    }
}

// Handle regular banner click
function handleRegularBannerClick(ad) {
    if (ad.click_url) {
        // Direct redirect for regular banners
        window.open(ad.click_url, '_blank');
    } else {
        // Show banner modal if no direct URL
        showBannerAdModal(ad);
    }
}

function showBannerAdModal(ad) {
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'banner-ad-modal';

    // Create modal content
    modal.innerHTML = `
        <div class="banner-ad-modal-content">
            <button class="banner-ad-modal-close">&times;</button>
            <img class="banner-ad-modal-image" src="${ad.image_url}" alt="${ad.title || 'Advertisement'}">
            <h2 class="banner-ad-modal-title">${ad.title || 'Advertisement'}</h2>
            <p class="banner-ad-modal-description">${ad.description || ''}</p>
            <button class="banner-ad-modal-button">متابعة</button>
        </div>
    `;

    // Add event listeners
    modal.querySelector('.banner-ad-modal-close').addEventListener('click', () => {
        modal.remove();
    });

    modal.querySelector('.banner-ad-modal-button').addEventListener('click', () => {
        if (ad.click_url) {
            // Open URL in new tab
            window.open(ad.click_url, '_blank');
        }
        modal.remove();
    });

    // Close modal when clicking outside content
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });

    // Add to document
    document.body.appendChild(modal);
}

// --- Entry Subscription Ad Functions ---
async function fetchAndDisplayEntrySubscriptionAd() {
    // هذه اداة كانت مخصصة عرض إعانات ااشتراك كـ "مودا" عند ادخو.
    // بما أن امستخدم يريدها كـ "بانر"، فإننا سنعتمد عى داة fetchBannerAds
    // جب جميع ابانرات (بما في ذك بانرات ااشتراك) وعرضها.
    // ذك، هذه اداة ن تقوم بعرض أي شيء بنفسها، ب ستترك اأمر داة displayBannerAds.
    console.log('fetchAndDisplayEntrySubscriptionAd is now handled by fetchBannerAds for banner display.');
    // يمكننا هنا مسح اكاش اقديم إذا كان ا يزا موجودًا تجنب أي تداخ
    localStorage.removeItem('entry_subscription_ads_v1');
}

// Helper to find an eligible entry ad based on display_frequency
function findEligibleEntryAd(ads, userId) {
    for (const ad of ads) {
        const lastShownKey = `entry_ad_last_shown_${userId}_${ad.id}`;
        const lastShownTime = localStorage.getItem(lastShownKey);
        const now = Date.now();

        switch (ad.display_frequency) {
            case 'once':
                if (localStorage.getItem(`entry_ad_shown_once_${userId}_${ad.id}`)) {
                    continue; // Already shown once, skip
                }
                return ad; // Show this ad
            case 'once_per_session':
                // This is handled by not persisting the 'shown' state across sessions.
                // If the ad is fetched and displayed, it's considered shown for this session.
                // No need for specific localStorage check here, as it's session-based.
                return ad;
            case 'once_per_day':
                if (lastShownTime) {
                    const lastShownDate = new Date(parseInt(lastShownTime, 10));
                    const today = new Date();
                    if (lastShownDate.toDateString() === today.toDateString()) {
                        continue; // Already shown today, skip
                    }
                }
                return ad; // Show this ad
            case 'always':
                return ad; // Always show if active
            default:
                return ad; // Default to always show if frequency is not set or unknown
        }
    }
    return null; // No eligible ad found
}

// Helper to increment view count and update last shown time for entry ads
function incrementEntryAdViewCount(adId, userId) {
    const viewCountKey = `entry_ad_view_count_${userId}_${adId}`;
    let currentViews = parseInt(localStorage.getItem(viewCountKey) || '0', 10);
    localStorage.setItem(viewCountKey, (currentViews + 1).toString());

    const lastShownKey = `entry_ad_last_shown_${userId}_${adId}`;
    localStorage.setItem(lastShownKey, Date.now().toString());

    // For 'once' frequency, mark as permanently shown
    // Note: This assumes 'bannerAds' might contain entry ads, or you might need to refetch ad details here.
    // For robustness, it's better to pass the ad object itself or fetch it if needed.
    // For now, assuming 'ad' object is available from the calling context (fetchAndDisplayEntrySubscriptionAd)
    // or that the ad object is passed directly to this helper.
    // Let's assume the ad object is passed to this helper for simplicity.
    // If not, we'd need to fetch it: const ad = await supabaseClient.from(BANNER_ADS_TABLE).select('*').eq('id', adId).single();
    // For now, I'll remove the `ad` lookup here and rely on the `display_frequency` being part of the ad object passed.
    // The `findEligibleEntryAd` already filters based on `display_frequency`.
    // The `once` logic should be handled by `findEligibleEntryAd` and setting `entry_ad_shown_once`.
    // So, this function just increments view count and sets last shown time.
    // The `entry_ad_shown_once` is set in `findEligibleEntryAd` when `once` is matched.
    // No, `findEligibleEntryAd` only *checks* if it was shown once. It doesn't *set* it.
    // So, I need to set it here.
    const adFromGlobal = bannerAds.find(b => b.id === adId); // Try to find from global bannerAds (which might contain all types)
    if (adFromGlobal && adFromGlobal.display_frequency === 'once') {
        localStorage.setItem(`entry_ad_shown_once_${userId}_${adId}`, 'true');
    }
}

// New function to display the entry subscription ad modal
async function showEntrySubscriptionAdModal(ad) {
    // Check if a modal is already open to prevent multiple modals
    if (document.querySelector('.subscription-campaign-modal') || document.querySelector('.banner-ad-modal') || document.getElementById('language-selection-modal') || document.getElementById('install-instructions-modal') || document.getElementById('image-zoom-modal') || document.getElementById('network-error-message') || document.getElementById('app-announcement-modal') || document.getElementById('update-notification-modal') || document.getElementById('entry-subscription-ad-modal')) {
        console.log('Another modal is already open, skipping entry subscription ad modal.');
        return;
    }

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'entry-subscription-ad-modal';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;

    // Create modal content container
    const modalContent = document.createElement('div');
    modalContent.className = 'entry-subscription-modal-content';
    modalContent.style.cssText = `
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border-radius: 20px;
        padding: 30px;
        max-width: 90%;
        max-height: 90%;
        overflow-y: auto;
        text-align: center;
        border: 2px solid #ffd700;
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
        position: relative;
        animation: slideInUp 0.4s ease;
    `;

    // Determine language for display
    const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
    const isArabic = userLanguage === 'ar';

    const title = isArabic ? (ad.title_ar || ad.title || 'إعان اشتراك مجاني') : (ad.title_en || ad.title || 'Free Subscription Ad');
    const description = isArabic ? (ad.description_ar || ad.description || 'احص عى اشتراك مجاني فترة محدودة!') : (ad.description_en || ad.description || 'Get a free subscription for a limited time!');
    const buttonText = isArabic ? (ad.button_text_ar || ad.button_text || 'اشترك اآن') : (ad.button_text_en || ad.button_text || 'Subscribe Now');

    modalContent.innerHTML = `
        <button class="close-entry-subscription-modal" style="
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 10001;
        ">&times;</button>

        ${ad.image_url ? `
            <img src="${ad.image_url}" style="
                width: 100%;
                max-width: 250px;
                border-radius: 15px;
                margin-bottom: 20px;
                border: 2px solid #ffd700;
            ">
        ` : ''}

        <h2 style="
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.8rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        ">${title}</h2>

        <p style="
            color: white;
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 1.1rem;
        ">${description}</p>

        <button class="entry-subscribe-btn" style="
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        ">
            ${buttonText}
        </button>
    `;

    // Add event listeners
    modalContent.querySelector('.close-entry-subscription-modal').addEventListener('click', () => {
        modalOverlay.remove();
    });

    modalContent.querySelector('.entry-subscribe-btn').addEventListener('click', () => {
        modalOverlay.remove();
        if (ad.campaign_id) {
            showSubscriptionCampaignModal(ad.campaign_id);
        } else {
            console.warn('Entry subscription ad has no campaign_id, cannot show campaign modal.');
            showMessage('ا توجد حمة اشتراك مرتبطة بهذا اإعان.', 'warning');
        }
    });

    // Close modal when clicking outside content
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            modalOverlay.remove();
        }
    });

    // Add to document
    document.body.appendChild(modalOverlay);
}

// Show subscription campaign modal
async function showSubscriptionCampaignModal(campaignId) {
    try {
        // Fetch campaign details
        const { data: campaign, error } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('*')
            .eq('id', campaignId)
            .eq('is_active', true)
            .single();

        if (error || !campaign) {
            console.error('Error fetching campaign:', error);
            return;
        }

        // Check if user already has an active subscription
        const userId = generateUserId();
        const { data: existingSubscription } = await supabaseClient
            .from('user_subscriptions')
            .select('*')
            .eq('user_id', userId)
            .eq('campaign_id', campaignId)
            .eq('status', 'active')
            .single();

        if (existingSubscription) {
            showMessage('ديك اشتراك مجاني نشط بافع!', 'success');
            return;
        }

        // Create subscription modal
        const modal = document.createElement('div');
        modal.className = 'subscription-campaign-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        modal.innerHTML = `
            <div class="subscription-modal-content" style="
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border-radius: 20px;
                padding: 30px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                text-align: center;
                border: 2px solid #ffd700;
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
                position: relative;
            ">
                <button class="close-subscription-modal" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    z-index: 10001;
                ">&times;</button>

                ${campaign.popup_image_url ? `
                    <img src="${campaign.popup_image_url}" style="
                        width: 100%;
                        max-width: 200px;
                        border-radius: 15px;
                        margin-bottom: 20px;
                        border: 2px solid #ffd700;
                    ">
                ` : ''}

                <h2 style="
                    color: #ffd700;
                    margin-bottom: 15px;
                    font-size: 1.8rem;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                ">${campaign.title_ar}</h2>

                <p style="
                    color: white;
                    margin-bottom: 20px;
                    line-height: 1.6;
                    font-size: 1.1rem;
                ">${campaign.description_ar}</p>

                <div style="
                    background: rgba(255, 215, 0, 0.1);
                    border-radius: 15px;
                    padding: 20px;
                    margin-bottom: 25px;
                    border: 1px solid rgba(255, 215, 0, 0.3);
                ">
                    <h3 style="color: #ffd700; margin-bottom: 10px;">
                        ⭐ مدة ااشتراك امجاني
                    </h3>
                    <p style="color: white; font-size: 1.2rem; font-weight: bold;">
                        ${campaign.subscription_duration_days} يوم
                    </p>
                </div>

                <div style="
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                    flex-wrap: wrap;
                ">
                    <button class="start-tasks-btn" style="
                        background: linear-gradient(45deg, #ffd700, #ffcc00);
                        color: black;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                        transition: all 0.3s ease;
                        box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
                    ">
                        🚀 ابدأ امهام
                    </button>

                    <button class="cancel-subscription-btn" style="
                        background: rgba(255, 255, 255, 0.1);
                        color: white;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                        transition: all 0.3s ease;
                    ">
                        إغاء
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-subscription-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.cancel-subscription-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.start-tasks-btn').addEventListener('click', () => {
            modal.remove();
            showTasksModal(campaignId);
        });

        // Close modal when clicking outside content
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Add to document
        document.body.appendChild(modal);

    } catch (error) {
        console.error('Error showing subscription campaign modal:', error);
        showMessage('حدث خطأ أثناء تحمي تفاصي احمة', 'error');
    }
}

// Show tasks modal for subscription campaign
async function showTasksModal(campaignId) {
    try {
        // Fetch campaign tasks
        const { data: tasks, error } = await supabaseClient
            .from('campaign_tasks')
            .select(`
                *,
                task_types (
                    display_name_ar,
                    display_name_en,
                    icon
                )
            `)
            .eq('campaign_id', campaignId)
            .eq('is_required', true)
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error fetching campaign tasks:', error);
            showMessage('حدث خطأ أثناء تحمي امهام', 'error');
            return;
        }

        if (!tasks || tasks.length === 0) {
            showMessage('ا توجد مهام متاحة هذه احمة', 'warning');
            return;
        }

        // Create tasks modal
        const modal = document.createElement('div');
        modal.className = 'tasks-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        const tasksHTML = tasks.map((task, index) => `
            <div class="task-item" style="
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 15px;
                border-left: 4px solid #ffd700;
                transition: all 0.3s ease;
            ">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <i class="${task.task_types?.icon || 'fas fa-tasks'}" style="
                        color: #ffd700;
                        font-size: 1.5rem;
                        margin-left: 15px;
                    "></i>
                    <h4 style="color: white; margin: 0; flex: 1;">
                        ${index + 1}. ${task.title_ar}
                    </h4>
                    <span class="task-status" style="
                        background: rgba(255, 215, 0, 0.2);
                        color: #ffd700;
                        padding: 5px 10px;
                        border-radius: 20px;
                        font-size: 0.8rem;
                        font-weight: bold;
                    ">
                        مطوب
                    </span>
                </div>

                ${task.description_ar ? `
                    <p style="color: #ccc; margin-bottom: 15px; line-height: 1.5;">
                        ${task.description_ar}
                    </p>
                ` : ''}

                <button class="complete-task-btn" data-task-id="${task.id}" style="
                    background: linear-gradient(45deg, #ffd700, #ffcc00);
                    color: black;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 20px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">
                    ${task.task_type === 'app_download' ? 'تحمي اتطبيق' :
                      task.task_type === 'mod_download' ? 'تحمي امود' :
                      task.task_type === 'telegram_subscribe' ? 'اشتراك في اتييجرام' :
                      task.task_type === 'youtube_subscribe' ? 'اشتراك في ايوتيوب' :
                      'إكما امهمة'}
                </button>
            </div>
        `).join('');

        modal.innerHTML = `
            <div class="tasks-modal-content" style="
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border-radius: 20px;
                padding: 30px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                border: 2px solid #ffd700;
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
                position: relative;
            ">
                <button class="close-tasks-modal" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    z-index: 10001;
                ">&times;</button>

                <h2 style="
                    color: #ffd700;
                    text-align: center;
                    margin-bottom: 25px;
                    font-size: 1.8rem;
                ">🎯 امهام امطوبة</h2>

                <p style="
                    color: white;
                    text-align: center;
                    margin-bottom: 30px;
                    line-height: 1.6;
                ">أكم جميع امهام اتاية حصو عى ااشتراك امجاني</p>

                <div class="tasks-list">
                    ${tasksHTML}
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="check-completion-btn" style="
                        background: linear-gradient(45deg, #22c55e, #16a34a);
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                        margin-left: 15px;
                    ">
                        ✅ تحقق من اإكما
                    </button>

                    <button class="close-tasks-btn" style="
                        background: rgba(255, 255, 255, 0.1);
                        color: white;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                    ">
                        إغاق
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-tasks-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.close-tasks-btn').addEventListener('click', () => {
            modal.remove();
        });

        // Add listeners for task completion buttons
        modal.querySelectorAll('.complete-task-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.getAttribute('data-task-id');
                const task = tasks.find(t => t.id === taskId);
                if (task) {
                    handleTaskCompletion(task);
                }
            });
        });

        modal.querySelector('.check-completion-btn').addEventListener('click', () => {
            checkAllTasksCompletion(campaignId);
        });

        // Close modal when clicking outside content
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Add to document
        document.body.appendChild(modal);

    } catch (error) {
        console.error('Error showing tasks modal:', error);
        showMessage('حدث خطأ أثناء تحمي امهام', 'error');
    }
}

// Handle task completion with smart verification
async function handleTaskCompletion(task) {
    try {
        // Initialize smart verification system if not already done
        if (!window.smartVerificationSystem) {
            window.smartVerificationSystem = new SmartVerificationSystem(supabaseClient);
        }

        // Use smart verification system
        const userId = generateUserId();
        const result = await window.smartVerificationSystem.handleTaskClick(task.id, task);

        if (result.success) {
            // Task verified successfully
            console.log('Task verified successfully:', result);
        } else {
            // Task verification failed
            console.log('Task verification failed:', result);

            // Fallback to manual completion for backward compatibility
            if (task.target_url) {
                if (typeof Android !== 'undefined' && Android.openUrl) {
                    Android.openUrl(task.target_url);
                } else {
                    window.open(task.target_url, '_blank');
                }
                markTaskAsCompleted(task.id);
            }
        }
    } catch (error) {
        console.error('Error in smart task completion:', error);

        // Fallback to original method
        if (task.target_url) {
            if (typeof Android !== 'undefined' && Android.openUrl) {
                Android.openUrl(task.target_url);
            } else {
                window.open(task.target_url, '_blank');
            }
            markTaskAsCompleted(task.id);
        }
    }
}

// Mark task as completed
async function markTaskAsCompleted(taskId) {
    try {
        const userId = generateUserId();

        const { error } = await supabaseClient
            .from('user_task_progress')
            .upsert({
                user_id: userId,
                task_id: taskId,
                status: 'completed',
                completed_at: new Date().toISOString()
            });

        if (error) {
            console.error('Error marking task as completed:', error);
        } else {
            showMessage('تم إكما امهمة بنجاح!', 'success');
        }
    } catch (error) {
        console.error('Error in markTaskAsCompleted:', error);
    }
}

// Check if all tasks are completed with smart verification
async function checkAllTasksCompletion(campaignId) {
    try {
        // Initialize smart verification system if not already done
        if (!window.smartVerificationSystem) {
            window.smartVerificationSystem = new SmartVerificationSystem(supabaseClient);
        }

        const userId = generateUserId();

        // Use smart verification system for comprehensive check
        const result = await window.smartVerificationSystem.checkAllTasksCompletion(userId, campaignId);

        if (result.success) {
            // All tasks completed and subscription activated
            console.log('Subscription activated successfully:', result);
        } else {
            // Some tasks still need completion
            if (result.remaining_tasks) {
                showMessage(`يجب إكما ${result.remaining_tasks} مهمة أخرى حصو عى ااشتراك امجاني`, 'warning');
            } else if (result.error) {
                showMessage(`خطأ: ${result.error}`, 'error');
            }
        }

    } catch (error) {
        console.error('Error in smart task completion check:', error);

        // Fallback to original method
        try {
            const userId = generateUserId();

            // Get all required tasks for this campaign
            const { data: requiredTasks, error: tasksError } = await supabaseClient
                .from('campaign_tasks')
                .select('id')
                .eq('campaign_id', campaignId)
                .eq('is_required', true);

            if (tasksError) {
                console.error('Error fetching required tasks:', tasksError);
                return;
            }

            // Get user's completed tasks (check for both 'completed' and 'verified' status)
            const { data: completedTasks, error: progressError } = await supabaseClient
                .from('user_task_progress')
                .select('task_id')
                .eq('user_id', userId)
                .in('status', ['completed', 'verified'])
                .in('task_id', requiredTasks.map(t => t.id));

            if (progressError) {
                console.error('Error fetching user progress:', progressError);
                return;
            }

            if (completedTasks.length === requiredTasks.length) {
                // All tasks completed - activate subscription
                await activateUserSubscription(userId, campaignId);
            } else {
                const remaining = requiredTasks.length - completedTasks.length;
                showMessage(`يجب إكما ${remaining} مهمة أخرى حصو عى ااشتراك امجاني`, 'warning');
            }

        } catch (fallbackError) {
            console.error('Error in fallback task completion check:', fallbackError);
            showMessage('حدث خطأ أثناء اتحقق من امهام', 'error');
        }
    }
}

// Activate user subscription
async function activateUserSubscription(userId, campaignId) {
    try {
        // Get campaign details
        const { data: campaign, error: campaignError } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('subscription_duration_days')
            .eq('id', campaignId)
            .single();

        if (campaignError) {
            console.error('Error fetching campaign:', campaignError);
            return;
        }

        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + campaign.subscription_duration_days);

        // Create user subscription
        const { error: subscriptionError } = await supabaseClient
            .from('user_subscriptions')
            .upsert({
                user_id: userId,
                campaign_id: campaignId,
                status: 'active',
                started_at: startDate.toISOString(),
                expires_at: endDate.toISOString()
            });

        if (subscriptionError) {
            console.error('Error creating subscription:', subscriptionError);
            showMessage('حدث خطأ أثناء تفعي ااشتراك', 'error');
            return;
        }

        // Show success message
        showMessage(`🎉 تهانينا! تم تفعي اشتراكك امجاني مدة ${campaign.subscription_duration_days} يوم`, 'success');

        // Close all modals
        document.querySelectorAll('.tasks-modal, .subscription-campaign-modal').forEach(modal => {
            modal.remove();
        });

    } catch (error) {
        console.error('Error activating subscription:', error);
        showMessage('حدث خطأ أثناء تفعي ااشتراك', 'error');
    }
}

// Show message function
function showMessage(message, type = 'info') {
    // Remove existing messages
    document.querySelectorAll('.app-message').forEach(msg => msg.remove());

    const messageDiv = document.createElement('div');
    messageDiv.className = 'app-message';
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'linear-gradient(45deg, #22c55e, #16a34a)' :
                     type === 'error' ? 'linear-gradient(45deg, #ef4444, #dc2626)' :
                     type === 'warning' ? 'linear-gradient(45deg, #f59e0b, #d97706)' :
                     'linear-gradient(45deg, #3b82f6, #2563eb)'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: bold;
        z-index: 10002;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.3s ease-out;
    `;

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 5000);
}

// --- Fetch Featured Addons ---
async function fetchFeaturedAddons() {
    const cacheKey = 'featured_addons';
    const cachedData = localStorage.getItem(cacheKey);

    // Check if we have valid cached data
    if (cachedData) {
        try {
            const parsedData = JSON.parse(cachedData);
            // Check if the cache is still valid (less than 1 hour old)
            if (Date.now() - parsedData.timestamp < CACHE_DURATION_MS) {
                console.log('Using cached featured addons data');
                return parsedData.data;
            }
        } catch (e) {
            console.error('Error parsing cached featured addons data:', e);
            // Continue to fetch fresh data if there's an error parsing the cache
        }
    }

    console.log('Fetching fresh featured addons data from Supabase');
    try {
        // First, check if the featured_addons table exists
        const { error: tableCheckError } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .select('id', { count: 'exact', head: true })
            .limit(1);

        if (tableCheckError) {
            console.warn('Featured Addons table not found or not accessible:', tableCheckError.message);
            // Return empty array instead of null to avoid breaking the UI
            return [];
        }

        // Fetch the featured addon IDs from the featured_addons table
        const { data: featuredEntries, error: featuredError } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .select('*')
            .eq('is_active', true);

        if (featuredError) {
            console.error('Error fetching featured addons:', featuredError);
            return [];
        }

        if (!featuredEntries || featuredEntries.length === 0) {
            console.log('No featured addons found');
            return [];
        }

        // Extract the mod IDs
        const modIds = featuredEntries.map(entry => entry.mod_id);

        // Fetch the actual mod details from the mods table
        const { data: mods, error: modsError } = await supabaseClient
            .from('mods')
            .select('*')
            .in('id', modIds);

        if (modsError) {
            console.error('Error fetching mod details for featured addons:', modsError);
            return [];
        }

        if (!mods || mods.length === 0) {
            console.log('No mod details found for the featured addon IDs');
            return [];
        }

        // Cache the data
        try {
            const dataToCache = {
                timestamp: Date.now(),
                data: mods
            };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log('Featured addons data cached');
        } catch (e) {
            console.error('Error storing featured addons data in localStorage:', e);
        }

        return mods;
    } catch (error) {
        console.error('Unexpected error in fetchFeaturedAddons:', error);
        return [];
    }
}

// --- Optimized Supabase Data Fetching with Smart Caching ---
async function fetchModsFromSupabase(category = 'All', sortBy = 'created_at', ascending = false, limit = null) {
    // تحسين مفتاح اتخزين امؤقت
    const cacheKey = `mods_${category}_${sortBy}_${ascending}_${limit || 'none'}`;
    const cachedData = localStorage.getItem(cacheKey);

    // فحص اتخزين امؤقت أواً
    if (cachedData) {
        try {
            const { timestamp, data } = JSON.parse(cachedData);
            const cacheAge = Date.now() - timestamp;

            // استخدام cache أطو بيانات اأساسية (30 دقيقة)
            const cacheValidDuration = category === 'All' ? 30 * 60 * 1000 : CACHE_DURATION_MS;

            if (cacheAge < cacheValidDuration) {
                console.log(`📦 Using cached data for ${cacheKey} (age: ${Math.round(cacheAge/1000)}s)`);
                return data;
            } else {
                console.log(`🗑️ Cache expired for ${cacheKey}, removing...`);
                localStorage.removeItem(cacheKey);
            }
        } catch (e) {
            console.error("❌ Error parsing cached data:", e);
            localStorage.removeItem(cacheKey);
        }
    }

    console.log(`🔄 Fetching optimized data from Supabase for key: ${cacheKey}`);

    // اتحقق من اتصا اشبكة أواً
    if (!navigator.onLine) {
        console.warn('🌐 No internet connection detected');
        return await getFallbackCachedData(category, sortBy);
    }

    try {
        // اتحقق من صحة عمي Supabase
        if (!supabaseClient) {
            console.error('❌ Supabase client not initialized');
            return await getFallbackCachedData(category, sortBy);
        }

        // تحسين ااستعام - تحديد احقو امطوبة فقط توفير ابيانات
        const selectFields = 'id, name, description, description_ar, image_urls, category, downloads, likes, created_at, creator_name, creator_social_media, is_featured, is_popular, download_url';

        let query = supabaseClient
            .from('mods')
            .select(selectFields); // تحديد احقو امطوبة فقط

        // تطبيق فتر افئة مع اتحقق من صحة اقيم
        if (category && category !== 'All' && category !== 'News' && category !== 'Suggested') {
            // اتحقق من أن افئة صحيحة
            const validCategories = ['Addons', 'Shaders', 'Texture', 'Seeds', 'Maps'];
            if (validCategories.includes(category)) {
                query = query.eq('category', category);
            } else {
                console.warn(`⚠️ Invalid category: ${category}, using default`);
                category = 'Addons'; // استخدام فئة افتراضية
                query = query.eq('category', category);
            }
        }

        // تطبيق اترتيب مع تحسين
        if (category !== 'Suggested' && sortBy && typeof sortBy === 'string' && sortBy.trim() !== '') {
            // اتحقق من صحة حق اترتيب
            const validSortFields = ['created_at', 'downloads', 'likes', 'name'];
            if (validSortFields.includes(sortBy)) {
                query = query.order(sortBy, { ascending: ascending });
            } else {
                console.warn(`⚠️ Invalid sort field: ${sortBy}, using default`);
                query = query.order('created_at', { ascending: false });
            }
        } else if (category !== 'Suggested') {
            // ترتيب افتراضي محسن
            query = query.order('created_at', { ascending: false });
        }

        // تطبيق احد اأقصى مع تحسين أداء
        const optimizedLimit = limit || (category === 'All' ? 30 : 15); // حد أقصى محسن
        if (optimizedLimit > 0) {
            query = query.limit(optimizedLimit);
        }
        console.log(`📊 Applying optimized limit: ${optimizedLimit}`);

        const startTime = Date.now();
        const { data: items, error } = await query;
        const queryTime = Date.now() - startTime;

        if (items) {
            items.forEach(item => {
                if (!item.download_url || !item.download_url.startsWith('http')) {
                    console.warn(`[DataIssue] Mod ID: ${item.id}, Name: ${item.name}, Missing or invalid download_url from Supabase fetch: '${item.download_url}'.`);
                }
            });
        }

        if (error) {
            console.error(`❌ Error fetching data from Supabase for category "${category}":`, error);

            // تحسين اكتشاف أخطاء قاعدة ابيانات
            const isDatabaseError = error.code && (
                error.code === '400' ||           // Bad Request
                error.code === '406' ||           // Not Acceptable
                error.code === 'PGRST301' ||      // Supabase connection error
                error.code === 'PGRST116' ||      // Supabase timeout error
                error.code === '42P01' ||         // Table does not exist
                error.code === '42703'            // Column does not exist
            );

            const isNetworkError = error.message && (
                error.message.includes('Failed to fetch') ||
                error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                error.message.includes('ERR_NETWORK') ||
                error.message.includes('ERR_INTERNET_DISCONNECTED') ||
                error.message.includes('TypeError: Failed to fetch') ||
                error.message.includes('NetworkError')
            );

            if (isDatabaseError) {
                console.log('🗄️ Database error detected, checking table structure...');

                // محاوة إصاح مشاك قاعدة ابيانات
                await handleDatabaseError(category, error);

                // إرجاع ابيانات امحفوظة مؤقتاً
                return await getFallbackCachedData(category, sortBy);
            }

            if (isNetworkError) {
                console.log('🌐 Network error detected, trying cached data...');

                // استخدام network handler إذا كان متاحاً
                if (typeof networkHandler !== 'undefined') {
                    networkHandler.showNetworkStatus('مشكة في ااتصا باخادم', 'error', 5000);
                    networkHandler.isOnline = false;
                    networkHandler.showOfflineMode();
                } else {
                    showNetworkError();
                }

                return await getFallbackCachedData(category, sortBy);
            }

            // أخطاء أخرى
            console.error(`💥 Unexpected database error for category "${category}":`, error);
            return await getFallbackCachedData(category, sortBy);
        }

        if (!items) {
            console.log(`📭 No items found in Supabase for category "${category}".`);
            return []; // Return empty array if no items found
        }

        console.log(`✅ Fetched ${items.length} mods in ${queryTime}ms`);

        // تحسين اتخزين امؤقت مع ضغط ابيانات
        try {
            const dataToCache = {
                timestamp: Date.now(),
                data: items,
                queryTime: queryTime,
                category: category,
                limit: optimizedLimit
            };

            const compressedData = JSON.stringify(dataToCache);

            // فحص حجم ابيانات قب اتخزين
            if (compressedData.length < 5 * 1024 * 1024) { // أق من 5MB
                localStorage.setItem(cacheKey, compressedData);
                console.log(`💾 Data cached (${Math.round(compressedData.length/1024)}KB) for key: ${cacheKey}`);
            } else {
                console.warn('⚠️ Data too large to cache, skipping...');
                // تنظيف اتخزين امؤقت اقديم
                cleanOldCache();
            }
        } catch (e) {
            console.error("❌ Error storing data in localStorage:", e);
            // تنظيف اتخزين امؤقت إذا امتأ
            if (e.name === 'QuotaExceededError') {
                console.log('🧹 Storage full, cleaning old cache...');
                cleanOldCache();
            }
        }

        return items; // Return the freshly fetched items

    } catch (error) {
        console.error(`Unexpected error fetching/caching data for category "${category}":`, error);
        // Check if it's a network-related error
        if (error.message && (error.message.includes('Failed to fetch') ||
                              error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                              error.message.includes('NetworkError') ||
                              error.message.includes('TypeError: Failed to fetch'))) {
            showNetworkError();
        }
        return null; // Return null on unexpected error
    }
}

// --- دوا مساعدة معاجة اأخطاء ---

// داة حصو عى ابيانات امحفوظة مؤقتاً في حاة افش
async function getFallbackCachedData(category, sortBy) {
    try {
        // ابحث عن أي بيانات محفوظة مؤقتاً هذه افئة
        const fallbackKeys = [
            `mods_${category}_${sortBy}_false_none`,
            `mods_${category}_${sortBy}_false_10`,
            `mods_${category}_${sortBy}_false_15`,
            `mods_${category}_created_at_false_none`,
            `mods_${category}_created_at_false_10`
        ];

        for (const key of fallbackKeys) {
            const cachedDataStr = localStorage.getItem(key);
            if (cachedDataStr) {
                try {
                    const cachedDataObj = JSON.parse(cachedDataStr);
                    if (cachedDataObj && cachedDataObj.data && cachedDataObj.data.length > 0) {
                        console.log(`📦 Using fallback cached data for category "${category}" with key: ${key}`);
                        return cachedDataObj.data;
                    }
                } catch (parseError) {
                    console.warn(`⚠️ Error parsing cached data for key ${key}:`, parseError);
                    localStorage.removeItem(key);
                }
            }
        }

        console.log(`📭 No fallback cached data found for category "${category}"`);
        return []; // إرجاع مصفوفة فارغة بداً من null
    } catch (error) {
        console.error('❌ Error in getFallbackCachedData:', error);
        return [];
    }
}

// داة معاجة أخطاء قاعدة ابيانات
async function handleDatabaseError(category, error) {
    try {
        console.log(`🔧 Attempting to handle database error for category: ${category}`);

        // تسجي تفاصي اخطأ
        const errorDetails = {
            category: category,
            errorCode: error.code,
            errorMessage: error.message,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        };

        // حفظ تفاصي اخطأ محياً مراجعة
        const errorLog = JSON.parse(localStorage.getItem('databaseErrors') || '[]');
        errorLog.push(errorDetails);

        // ااحتفاظ بآخر 50 خطأ فقط
        if (errorLog.length > 50) {
            errorLog.splice(0, errorLog.length - 50);
        }

        localStorage.setItem('databaseErrors', JSON.stringify(errorLog));

        // محاوة إعادة تهيئة اجداو إذا كان اخطأ متعق بعدم وجود جدو
        if (error.code === '42P01' || error.message.includes('does not exist')) {
            console.log('🔄 Table does not exist, attempting to reinitialize...');

            if (typeof supabaseManager !== 'undefined') {
                await supabaseManager.initializeTables();
            }
        }

        // إرسا تقرير اخطأ إى قاعدة ابيانات إذا أمكن
        if (typeof supabaseClient !== 'undefined' && supabaseClient) {
            try {
                await supabaseClient
                    .from('error_reports')
                    .insert([errorDetails]);
                console.log('✅ Error report sent to database');
            } catch (reportError) {
                console.warn('⚠️ Could not send error report:', reportError);
            }
        }

    } catch (handlingError) {
        console.error('❌ Error in handleDatabaseError:', handlingError);
    }
}

// --- داة تنظيف اتخزين امؤقت اقديم ---
function cleanOldCache() {
    try {
        console.log('🧹 Starting cache cleanup...');
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة
        let cleanedCount = 0;

        // فحص جميع مفاتيح localStorage
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('mods_')) {
                try {
                    const data = JSON.parse(localStorage.getItem(key));
                    if (data && data.timestamp && (now - data.timestamp > maxAge)) {
                        keysToRemove.push(key);
                    }
                } catch (e) {
                    // إزاة ابيانات اتافة
                    keysToRemove.push(key);
                }
            }
        }

        // إزاة ابيانات اقديمة
        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            cleanedCount++;
        });

        console.log(`✅ Cache cleanup completed: removed ${cleanedCount} old entries`);

        // إذا كان اتخزين ا يزا ممتئاً، إزاة امزيد
        if (cleanedCount === 0) {
            console.log('🗑️ Forcing cache cleanup...');
            const allCacheKeys = Object.keys(localStorage).filter(key =>
                key.startsWith('mods_') || key.includes('_cache_') || key.includes('_timestamp_')
            );

            // إزاة نصف ابيانات امحفوظة
            const keysToForceRemove = allCacheKeys.slice(0, Math.ceil(allCacheKeys.length / 2));
            keysToForceRemove.forEach(key => localStorage.removeItem(key));
            console.log(`🗑️ Force removed ${keysToForceRemove.length} cache entries`);
        }

    } catch (error) {
        console.error('❌ Error during cache cleanup:', error);
    }
}

// --- Supabase Data Interaction ---

async function toggleLike(modId, modName, button) {
    console.log(`>>> toggleLike called for mod ID: ${modId}`); // Added entry log
    const userId = generateUserId();
    console.log(`Toggling like for mod ID: ${modId} by user: ${userId}`); // Debug log

    try {
        const { data: rpcResult, error: rpcError } = await supabaseClient.rpc('toggle_like', {
            mod_id_in: modId,
            user_id_in: userId
        });

        if (rpcError) {
            console.error("Error calling toggle_like RPC:", rpcError);
            alert("Error updating like.");
            return;
        }
        if (!rpcResult) {
            console.error("RPC function 'toggle_like' did not return expected data.");
            alert("Unexpected error updating like.");
            return;
        }

        const { liked, new_count: finalLikes } = rpcResult;
        console.log(`Like status for ${modId}: ${liked}, New count: ${finalLikes}`);

        updateCountUI(modId, 'like', finalLikes);

        document.querySelectorAll(`.like-button[onclick*="'${modId}'"]`).forEach(btn => {
             const heartIcon = btn.querySelector(".heart-icon");
             if (heartIcon) {
                 if (liked) {
                    heartIcon.classList.remove("animate-like");
                    void heartIcon.offsetWidth;
                    heartIcon.classList.add("animate-like");
                    heartIcon.style.color = "red";
                    btn.classList.add("liked");
                 } else {
                    heartIcon.classList.remove("animate-like");
                    heartIcon.style.color = "#f1f5f9";
                    btn.classList.remove("liked");
                 }
             }
        });

    } catch (error) {
        console.error("Error in toggleLike function:", error);
        alert("An unexpected error occurred.");
    }
}

// --- Function called by Android to update download progress ---
// Now accepts an additional 'status' string parameter
function updateDownloadProgress(modId, progress, status) {
    console.log(`>>> updateDownloadProgress called for mod ID: ${modId}, Progress: ${progress}%, Status: ${status}`);
    if (!modId || progress === undefined || progress === null) {
        console.error("updateDownloadProgress called with invalid arguments", modId, progress, status);
        return;
    }

    // Find the progress bar and text elements within the modal (assuming modal is open)
    // We target elements specifically associated with the modId
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButtonText = document.querySelector(`.download-btn[data-mod-id="${modId}"] .download-btn-text`); // Find text inside button

    if (progressBar && progressText) {
        const percentage = Math.max(0, Math.min(100, parseInt(progress, 10))); // Clamp between 0-100
        progressBar.style.width = `${percentage}%`;

        // Update the text content to include the status if provided
        if (status && status.trim() !== "") {
            progressText.textContent = `${status} (${percentage}%)`;
        } else {
            // Fallback to just percentage if status is empty or null
            progressText.textContent = `${percentage}%`;
        }


        // Optionally hide the main "Download" text inside the button while progress shows
        if (downloadButtonText) {
            downloadButtonText.style.display = 'none';
        }

        // Make sure the container is visible
        const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
        if (progressContainer) {
            progressContainer.style.display = 'flex'; // Or 'block' depending on styling
        }
    } else {
        // This might happen if the modal is closed while download is in progress
        console.warn(`Progress elements not found for mod ID: ${modId} in updateDownloadProgress. Modal might be closed.`);
    }
}


// --- Function called by Android after successful download to mark it ---
function markModAsDownloaded(modId) {
    console.log(`>>> markModAsDownloaded called for mod ID: ${modId}`); // Added log
    if (!modId) {
        console.error("markModAsDownloaded called with invalid modId");
        return;
    }
    const key = `downloaded_${modId}`;
    try {
        localStorage.setItem(key, 'true');
        console.log(`Mod ${modId} marked as downloaded in localStorage.`);

        // --- Update UI to reflect downloaded state ---
        const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
        const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
        const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
        const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
        const downloadButtonText = downloadButton?.querySelector('.download-btn-text'); // Find text inside button

        if (progressContainer) {
            progressContainer.style.display = 'none'; // Hide progress container
        }
        if (progressBar) {
            progressBar.style.width = '0%'; // Reset progress bar
        }
        if (progressText) {
            progressText.textContent = ''; // Clear progress text
        }
        if (downloadButtonText) {
            downloadButtonText.textContent = 'Open'; // Change button text to "Open"
            downloadButtonText.style.display = 'inline'; // Ensure text is visible
        }
         if (downloadButton) {
            // Add class to change style for downloaded state
            downloadButton.classList.add('downloaded');
            // REMOVED: Do not replace the onclick handler.
            // The original handleDownload function will correctly route to openDownloadedMod
            // when it checks localStorage again.
            downloadButton.disabled = false; // Ensure button is enabled after download
        }

        // تحديث الأزرار المباشرة أيضاً
        updateDirectDownloadButtons(modId);
        // --- End UI Update ---

    } catch (e) {
        console.error(`Error setting localStorage item for key ${key}:`, e);
    }
}

// دالة التحميل المباشر للأزرار الجديدة
async function handleDirectDownload(modId, modName, downloadLink) {
    console.log(`🔄 Direct download initiated for mod: ${modName} (ID: ${modId})`);

    // التحقق من صحة البيانات
    if (!modId || !modName || !downloadLink) {
        console.error('❌ Missing required parameters for direct download');
        showDownloadErrorMessage(modName || 'Unknown Mod', 'بيانات غير مكتملة للتحميل');
        return;
    }

    // التحقق من صحة رابط التحميل
    if (!downloadLink.startsWith('http')) {
        console.error('❌ Invalid download link for direct download:', downloadLink);
        showDownloadErrorMessage(modName, 'رابط التحميل غير صحيح');
        return;
    }

    // التحقق من حالة التحميل السابقة
    const isAlreadyDownloaded = localStorage.getItem(`downloaded_${modId}`) === 'true';
    if (isAlreadyDownloaded) {
        console.log(`📂 Mod already downloaded, attempting to open: ${modName}`);
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.openDownloadedMod) {
            AndroidInterface.openDownloadedMod(modId, modName, downloadLink);
        } else {
            showDownloadErrorMessage(modName, 'لا يمكن فتح الملف المحمل');
        }
        return;
    }

    // العثور على زر التحميل وتحديث حالته
    const downloadBtn = document.querySelector(`.direct-download-btn[data-mod-id="${modId}"]`);
    const progressRing = document.querySelector(`.download-progress-ring[data-mod-id="${modId}"]`);

    if (downloadBtn) {
        // تعطيل الزر أثناء التحميل
        downloadBtn.disabled = true;
        downloadBtn.style.opacity = '0.7';

        // إظهار مؤشر التقدم
        if (progressRing) {
            progressRing.style.display = 'block';
            progressRing.style.border = '2px solid #ffc107';
            progressRing.style.animation = 'spin 1s linear infinite';
        }
    }

    try {
        // بدء عملية التحميل
        await downloadModFileWithFallback(modId, modName, downloadLink);

    } catch (error) {
        console.error('❌ Error in direct download:', error);
        showDownloadErrorMessage(modName, 'حدث خطأ أثناء التحميل');

        // إعادة تعيين حالة الزر
        if (downloadBtn) {
            downloadBtn.disabled = false;
            downloadBtn.style.opacity = '1';
        }
        if (progressRing) {
            progressRing.style.display = 'none';
        }
    }
}

// تحديث دالة markModAsDownloaded لتحديث الأزرار المباشرة أيضاً
function updateDirectDownloadButtons(modId) {
    const directDownloadBtns = document.querySelectorAll(`.direct-download-btn[data-mod-id="${modId}"]`);
    const progressRings = document.querySelectorAll(`.download-progress-ring[data-mod-id="${modId}"]`);

    directDownloadBtns.forEach(btn => {
        btn.disabled = false;
        btn.style.opacity = '1';
        btn.style.background = '#28a745'; // أخضر للمحمل
        btn.classList.add('downloaded');
        btn.title = 'فتح المود';

        // تحديث الأيقونة
        const icon = btn.querySelector('i');
        if (icon) {
            icon.className = 'fa-solid fa-folder-open';
        }
    });

    progressRings.forEach(ring => {
        ring.style.display = 'none';
    });
}

async function handleDownload(modId, modName, downloadLink) {
    console.log(`>>> handleDownload called for mod ID: ${modId}, Name: ${modName}`);

    // Validate downloadLink early
    if (!downloadLink || typeof downloadLink !== 'string' || !downloadLink.startsWith('http')) {
        console.error('❌ Invalid or missing download link provided to handleDownload for mod:', modName, downloadLink);
        showDownloadErrorMessage(modName, t('download_link_unavailable')); // Use translation for user message
        resetDownloadButtonUI(modId);
        return; // Stop further processing
    }

    const isAlreadyDownloaded = localStorage.getItem(`downloaded_${modId}`) === 'true';

    // --- Find UI elements for progress ---
    const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
    const downloadButtonText = downloadButton?.querySelector('.download-btn-text');
    // --- End Find UI elements ---

    if (isAlreadyDownloaded) {
        console.log(`Mod ${modId} (${modName}) is already downloaded. Attempting to open.`);
        // Ensure button text is 'Open' if modal was re-opened for an already downloaded mod
        if (downloadButtonText) downloadButtonText.textContent = 'Open';
        if (downloadButton) downloadButton.classList.add('downloaded');

        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.openDownloadedMod) {
            // The onclick handler should already be set correctly by markModAsDownloaded
            // or during modal creation if it was already downloaded.
            // Call the native function to open the downloaded mod file.
            AndroidInterface.openDownloadedMod(modId, modName || 'mod', downloadLink);
            console.log(`Called AndroidInterface.openDownloadedMod for mod ID: ${modId}`);
        } else {
            console.warn('AndroidInterface.openDownloadedMod not found. Cannot open file.');
            alert('Cannot open the already downloaded file. Please find it in your downloads folder.');
        }
        return; // Stop here if already downloaded
    }

    // --- Check if should show subscription before download ---
    const showSubscriptionBeforeDownload = localStorage.getItem('showSubscriptionBeforeDownload') === 'true';

    if (showSubscriptionBeforeDownload) {
        // Check if user has active subscription
        const hasActiveSubscription = await checkActiveSubscription();

        if (!hasActiveSubscription) {
            // Show subscription modal/page before download
            const useSubscriptionPage = localStorage.getItem('useSubscriptionPage') === 'true';
            const activeCampaign = await getFirstActiveCampaign();

            if (activeCampaign) {
                // Store download info for after subscription
                localStorage.setItem('pendingDownload', JSON.stringify({
                    modId: modId,
                    modName: modName,
                    downloadLink: downloadLink
                }));

                if (useSubscriptionPage) {
                    // Close current modal
                    closeModal();
                    // Redirect to subscription page
                    window.location.href = `subscription-page.html?campaign=${activeCampaign.id}&return=download`;
                } else {
                    // Show subscription modal with skip option
                    showSubscriptionBeforeDownloadModal(activeCampaign, modId, modName, downloadLink);
                }
                return;
            }
        }
    }
    // --- End subscription check ---

    // --- Check if download is already in progress (progress container is visible) ---
    if (progressContainer && progressContainer.style.display !== 'none') {
        console.log(`Download for mod ${modId} (${modName}) is already in progress. Ignoring click.`);
        return; // Do nothing if progress is already showing
    }
    // --- End Check ---

    // --- If not downloaded and not in progress, proceed with download ---
    console.log(`Mod ${modId} (${modName}) not downloaded yet. Starting download process.`);

    // --- Show and initialize progress UI ---
    if (progressContainer) progressContainer.style.display = 'flex'; // Show container
    if (progressBar) progressBar.style.width = '0%'; // Reset bar
    if (progressText) progressText.textContent = '0%'; // Set initial text
    if (downloadButtonText) downloadButtonText.style.display = 'none'; // Hide "Download" text
    // REMOVED: Do not disable the button; the overlay indicates activity.
    // if (downloadButton) downloadButton.disabled = true;
    // --- End Progress UI Init ---

    // استخدام انظام امحسن مع ااحتياطي
    downloadModFileWithFallback(modId, modName, downloadLink);
}

// Enhanced download function with fallback system
async function downloadModFileWithFallback(modId, modName, downloadLink) {
    console.log('🔄 بدء تحميل المود مع النظام الاحتياطي:', modName);

    // استخدام النظام العادي أولاً (مع الإعلانات المكافئة)
    // نظام downloadFallbackSystem سيتم استخدامه فقط كـ fallback في حالة فشل النظام العادي
    return downloadModFile(modId, modName, downloadLink);
}

// Helper function to contain the actual download logic
function downloadModFile(modId, modName, downloadLink) {
    const encodedLink = encodeURI(downloadLink || '');
    if (!encodedLink || !encodedLink.startsWith('http')) {
        console.warn('Invalid or missing download link for mod:', modName);
        alert('Download link is unavailable or invalid!');
        // --- Reset UI if download fails early ---
        resetDownloadButtonUI(modId);
        // --- End Reset UI ---
        return;
    }

    if (typeof AndroidInterface !== 'undefined' && AndroidInterface.startModDownload) {
        console.log(`Calling native download for ${modName} from ${encodedLink}`);
        try {
            // Use the original function that might trigger an ad request first
            AndroidInterface.requestModDownloadWithAd(modId, modName || 'mod', encodedLink); // Re-enabled ad request
            // Or call startModDownload directly if requestModDownloadWithAd handles the check
            // AndroidInterface.startModDownload(modId, modName || 'mod', encodedLink); // Disabled direct download
        } catch (error) {
            console.error("Error calling AndroidInterface download function:", error);
            alert('Error starting download.');
            // --- Reset UI on error ---
            resetDownloadButtonUI(modId);
            // --- End Reset UI ---
        }
    } else {
        console.warn('AndroidInterface not found. Opening link in new tab as fallback.');
        alert('Automatic download cannot be started. The link will be opened in the browser.');
        // --- Reset UI if using fallback ---
        resetDownloadButtonUI(modId);
        // --- End Reset UI ---
        window.open(encodedLink, '_blank');
    }

    // REMOVED: Immediate RPC call to increment downloads.
    // The native code will now handle incrementing after successful download
    // and call androidDidIncrementDownloadCount to update the UI.
}

// تسجي إحصائيات اتحمي
async function recordDownloadStatistics(modId, success, usedBackup, errorType = null, errorMessage = '') {
    try {
        if (typeof supabaseClient !== 'undefined' && supabaseClient) {
            // استدعاء داة تحديث اإحصائيات
            const { error } = await supabaseClient.rpc('update_download_statistics', {
                p_mod_id: modId,
                p_success: success,
                p_used_backup: usedBackup,
                p_error_type: errorType,
                p_download_time: null // يمكن إضافة قياس اوقت احقاً
            });

            if (error) {
                console.error('❌ خطأ في تسجي إحصائيات اتحمي:', error);
            } else {
                console.log('✅ تم تسجي إحصائيات اتحمي بنجاح');
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تسجي اإحصائيات:', error);
    }
}

// عرض رساة نجاح اتحمي
function showDownloadSuccessMessage(modName, usedBackup = false) {
    const message = usedBackup
        ? `تم تحمي "${modName}" بنجاح من ارابط ااحتياطي`
        : `تم تحمي "${modName}" بنجاح`;

    const alertClass = usedBackup ? 'alert-warning' : 'alert-success';
    const icon = usedBackup ? 'fas fa-shield-alt' : 'fas fa-check-circle';

    showCustomAlert(message, alertClass, icon);

    // إضافة تأثير بصري نجاح
    if (usedBackup) {
        console.log('🛡️ تم استخدام ارابط ااحتياطي تحمي');
    }
}

// عرض رساة خطأ اتحمي
function showDownloadErrorMessage(modName, errorMessage) {
    const message = `فش تحمي "${modName}": ${errorMessage}`;
    showCustomAlert(message, 'alert-danger', 'fas fa-exclamation-triangle');
}

// عرض تنبيه مخصص
function showCustomAlert(message, alertClass = 'alert-info', icon = 'fas fa-info-circle') {
    // إزاة اتنبيهات اسابقة
    const existingAlerts = document.querySelectorAll('.download-alert');
    existingAlerts.forEach(alert => alert.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed download-alert`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
    `;
    alertDiv.innerHTML = `
        <i class="${icon}" style="margin-left: 8px;"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزاة اتنبيه تقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// --- Helper to reset button UI if download fails or is cancelled ---
function resetDownloadButtonUI(modId) {
    const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
    const downloadButtonText = downloadButton?.querySelector('.download-btn-text');

    if (progressContainer) progressContainer.style.display = 'none';
    if (progressBar) progressBar.style.width = '0%';
    if (progressText) progressText.textContent = '';
    if (downloadButtonText) {
        downloadButtonText.textContent = 'Download'; // Restore original text
        downloadButtonText.style.display = 'inline';
    }
    if (downloadButton) {
        downloadButton.disabled = false; // Ensure button is re-enabled
        downloadButton.classList.remove('downloaded'); // Ensure downloaded state is removed
        // No need to restore onclick, as we didn't replace it.
        // The original handleDownload attached in showModal remains.
        // We need the original modName and downloadLink here.
        // This suggests we might need to store them temporarily or fetch them again.
        // For now, let's assume they are available in scope or we fetch item data again.
        // A simpler approach might be to just re-enable the button and let the user click again.
        // Let's stick to re-enabling for now. The original onclick might still be attached
        // if we didn't overwrite it, just disabled the button.
    }
}


// --- Native Callback Handler ---
// Called by Android native code after a download is successfully completed AND counted (once per user/mod)
function androidDidIncrementDownloadCount(modId) {
    console.log(`Native code confirmed download increment for mod ID: ${modId}`);

    // Find *one* of the count elements for this mod to get the current value
    const countElement = document.querySelector(`.download-count[data-mod-id="${modId}"]`);

    if (countElement) {
        const currentCountText = countElement.textContent || '0';
        // Attempt to parse the current count, handling K/M suffixes if necessary
        let currentCount = 0;
        if (currentCountText.endsWith('K')) {
            currentCount = parseFloat(currentCountText.replace('K', '')) * 1000;
        } else if (currentCountText.endsWith('M')) {
            currentCount = parseFloat(currentCountText.replace('M', '')) * 1000000;
        } else {
            currentCount = parseInt(currentCountText, 10);
        }

        if (!isNaN(currentCount)) {
            const newCount = currentCount + 1;
            console.log(`Updating UI for mod ${modId} to new download count: ${newCount}`);
            // Use the existing UI update function
            updateCountUI(modId, 'download', newCount);
        } else {
            console.warn(`Could not parse current download count '${currentCountText}' for mod ${modId}. Updating UI to '1'.`);
            // Fallback if parsing fails (e.g., initial state was not a number)
             updateCountUI(modId, 'download', 1);
        }
    } else {
        console.warn(`Could not find download count element for mod ID: ${modId} in script.js to update count.`);
    }
}

// --- Native Callback Handler for Persistence ---
// Called by Android native code after a download is successfully counted locally,
// instructing the JS to update the count in the database.
async function androidShouldPersistDownloadIncrement(modId) {
    console.log(`Native code requested persistence for download increment for mod ID: ${modId}`);

    // اتحقق من وجود supabaseClient
    if (!supabaseClient) {
        console.warn('Supabase client not available, skipping download increment persistence');
        return;
    }

    try {
        // Call the Supabase RPC function to increment the count, using the correct parameter name
        const { error: rpcError } = await supabaseClient.rpc('increment_downloads', { mod_id_in: modId });

        if (rpcError) {
            // Log the error, providing more detail if possible
            console.error(`Error calling Supabase RPC 'increment_downloads' for mod ${modId}:`, JSON.stringify(rpcError, null, 2));

            // إذا كانت امشكة أن اداة غير موجودة، اعرض رساة مفيدة
            if (rpcError.code === '42883' || rpcError.message?.includes('function') || rpcError.message?.includes('does not exist')) {
                console.warn('⚠️ increment_downloads function not found in database. Please execute database/QUICK_FIX_RPC_FUNCTIONS.sql');
            }

            // Also log the message property if it exists
            if (rpcError.message) {
                console.error("RPC Error Message:", rpcError.message);
            }
        } else {
            console.log(`✅ Successfully called Supabase RPC 'increment_downloads' for mod ${modId}.`);
            // Optional: Invalidate cache for this mod or category if needed,
            // although the UI is already updated optimistically.
        }
    } catch (error) {
        console.error(`💥 Unexpected error in androidShouldPersistDownloadIncrement for mod ${modId}:`, error);

        // ا نوقف اتطبيق بسبب خطأ في اإحصائيات
        console.warn('🔄 Continuing app execution despite download tracking error');
    }
}


// --- Element Creation Helper ---
function createModElement(item, elementType = 'item') { // Default to 'item'
    const element = document.createElement('div');
    element.className = elementType; // 'item' or 'mod-card'

    element.setAttribute('data-id', item.id);
    element.setAttribute('data-likes', item.likes || 0);
    element.setAttribute('data-downloads', item.downloads || 0);
    element.setAttribute('data-date', item.created_at || new Date(0).toISOString());
    if (elementType === 'item') { // Add name attribute only for horizontal items if needed
        element.setAttribute('data-name', item.name || 'Unnamed Mod');
    }

    // Check if mod is new (less than 7 days old)
    const isNew = isRecentMod(item.created_at);

    const altText = (item.name && item.name.toLowerCase() !== 'all') ? item.name : 'Mod Image';

    let mainImage = 'image/placeholder.svg';
    if (item.image_urls) {
        if (Array.isArray(item.image_urls)) {
            const validUrls = item.image_urls.filter(url => typeof url === 'string' && url.startsWith('http'));
            if (validUrls.length > 0) mainImage = validUrls[0];
        } else if (typeof item.image_urls === 'string') {
            try {
                const parsedImages = JSON.parse(item.image_urls);
                if (Array.isArray(parsedImages)) {
                    const validUrls = parsedImages.filter(url => typeof url === 'string' && url.startsWith('http'));
                    if (validUrls.length > 0) mainImage = validUrls[0];
                } else if (item.image_urls.startsWith('http')) {
                    mainImage = item.image_urls;
                }
            } catch (e) {
                if (item.image_urls.startsWith('http')) {
                    mainImage = item.image_urls;
                }
            }
        }
    }

     // Generate Inner HTML based on elementType
     if (elementType === 'item') { // Horizontal scroll item
         // تحديد اأيقونات امطوبة
         let iconsHtml = '';

         // أيقونة Free Addons
         if (item.is_free_addon) {
             iconsHtml += '<div class="free-addon-icon">Free Addons</div>';
         }

         // أيقونة Popular (اأكثر شعبية)
         if (isModPopular && isModPopular(item)) {
             iconsHtml += '<div class="popular-icon">Popular</div>';
         }

         // أيقونة NEW (جديد)
         if (isModNew && isModNew(item)) {
             iconsHtml += '<div class="new-badge">NEW</div>';
         }

         // عرض مباشر صور مع اأيقونات
         element.innerHTML = `
            <div class="mod-image-container">
                <img src="${mainImage}" alt="${altText}" class="mod-image" loading="lazy" onerror="this.src='image/placeholder.svg'">
                ${iconsHtml}
            </div>
            <h3 class="mod-name">${item.name || 'Unnamed Mod'}</h3>
            <div class="mod-actions">
                <div class="action-item">
                    <img src="image/Download icon.png" alt="Download Icon" class="action-icon">
                    <span class="action-count download-count" data-mod-id="${item.id}">${formatCount(item.downloads || 0)}</span>
                </div>
                <div class="action-item">
                    <img src="image/minecraft-icon.png" alt="Minecraft Icon" class="action-icon">
                    <p class="platform-label" style="font-size: 0.8rem; margin-top: 2px;">Bedrock</p>
                </div>
                <div class="action-item like-action">
                    <button class="like-button" onclick="event.stopPropagation(); toggleLike('${item.id}', '${item.name || 'Unnamed Mod'}', this)" title="Like this mod" style="background: transparent; border: none; padding: 0; cursor: pointer;">
                        <img src="image/heart.png" alt="Like Icon" class="heart-icon action-icon">
                    </button>
                    <span class="like-count action-count" data-mod-id="${item.id}">${formatCount(item.likes || 0)}</span>
                </div>
            </div>
        `;
     } else { // elementType === 'mod-card' - Vertical list/grid card
         // تحديد اأيقونات امطوبة
         let iconsHtml = '';

         // أيقونة Free Addons
         if (item.is_free_addon) {
             iconsHtml += '<div class="free-addon-icon">Free Addons</div>';
         }

         // أيقونة Popular (اأكثر شعبية)
         if (isModPopular && isModPopular(item)) {
             iconsHtml += '<div class="popular-icon">Popular</div>';
         }

         // أيقونة NEW (جديد)
         if (isModNew && isModNew(item)) {
             iconsHtml += '<div class="new-badge">NEW</div>';
         }

         // التحقق من حالة التحميل
         const isDownloaded = localStorage.getItem(`downloaded_${item.id}`) === 'true';

         // التحقق من صحة رابط التحميل
         const hasValidDownloadLink = item.download_url && item.download_url.startsWith('http');
         if (!hasValidDownloadLink) {
             console.warn(`[DownloadLinkIssue] Mod ID: ${item.id}, Name: ${item.name}, Invalid download_url: '${item.download_url}' at createModElement.`);
         }

         // عرض مباشر صور مع اأيقونات
         element.innerHTML = `
            <div class="mod-image-container" style="height: 180px; position: relative;">
                <img src="${mainImage}" alt="${altText}" class="mod-image" loading="lazy" onerror="this.src='image/placeholder.svg'">
                ${iconsHtml}
            </div>
            <div class="mod-info">
                <h3 class="mod-name">${item.name || 'Unnamed Mod'}</h3>
                <div class="mod-actions">
                    <div class="action-item">
                        <img src="image/Download icon.png" alt="Download Icon" class="action-icon">
                        <span class="action-count download-count" data-mod-id="${item.id}">${formatCount(item.downloads || 0)}</span>
                    </div>
                    <div class="action-item">
                        <img src="image/minecraft-icon.png" alt="Minecraft Icon" class="action-icon">
                        <p class="platform-label" style="font-size: 0.8rem; margin-top: 2px;">Bedrock</p>
                    </div>
                    <div class="action-item like-action">
                        <button class="like-button" onclick="event.stopPropagation(); toggleLike('${item.id}', '${item.name || 'Unnamed Mod'}', this)" title="Like this mod" style="background: transparent; border: none; padding: 0; cursor: pointer;">
                            <img src="image/heart.png" alt="Like Icon" class="heart-icon action-icon">
                        </button>
                        <span class="like-count action-count" data-mod-id="${item.id}">${formatCount(item.likes || 0)}</span>
                    </div>
                </div>
            </div>
         `;
     }

    // Add click listener directly to the element to show the modal
    element.addEventListener('click', (event) => {
        // Check if the click originated from a button inside the element
        if (event.target.closest('button')) {
            console.log(`Click detected on a button inside mod item (ID: ${item.id}). Stopping modal opening.`);
            // event.stopPropagation(); // Stop propagation if needed, but onclick on buttons should handle their actions
            return; // Don't open the modal if a button was clicked
        }

        // If the click was not on a button, proceed to show the modal
        console.log(`Mod item area clicked (ID: ${item.id}). Attempting to show modal.`);

        if (typeof showModal === 'function') {
            // Ensure 'item' is valid before calling
            if (item && item.id) {
                 console.log("Calling showModal with item:", item); // Log before calling
                 showModal(item);
            } else {
                 console.error("Invalid 'item' data passed to click listener for element:", element);
            }
        } else {
            console.error("showModal function is not defined.");
        }
    });

    return element;
}


// --- Display Logic ---

// في مف JavaScript اخاص بانسخة اسابقة

// ... (اكود احاي داة displayModsBySection) ...

// New function to fetch suggested mods
async function fetchSuggestedModsFromSupabase(limit = 10) {
    const cacheKey = `suggested_mods_${limit}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
        try {
            const { timestamp, data } = JSON.parse(cachedData);
            if (Date.now() - timestamp < CACHE_DURATION_MS) {
                console.log(`Using cached suggested mods data for key: ${cacheKey}`);
                return data;
            } else {
                console.log(`Cache expired for suggested mods key: ${cacheKey}`);
                localStorage.removeItem(cacheKey);
            }
        } catch (e) {
            console.error("Error parsing cached suggested_mods data:", e);
            localStorage.removeItem(cacheKey);
        }
    }

    console.log(`Fetching fresh suggested mods from Supabase (limit: ${limit})`);
    try {
        // Step 1: Fetch mod_ids and their display_order from suggested_mods table
        const { data: suggestedEntries, error: suggestedError } = await supabaseClient
            .from(SUGGESTED_MODS_TABLE)
            .select('mod_id, display_order')
            .order('display_order', { ascending: true })
            .limit(limit);

        if (suggestedError) {
            console.error('Error fetching from suggested_mods table:', suggestedError);
            return null;
        }

        if (!suggestedEntries || suggestedEntries.length === 0) {
            console.log('No entries found in suggested_mods table.');
            return [];
        }

        const modIds = suggestedEntries.map(entry => entry.mod_id);
        const orderMap = new Map(suggestedEntries.map(entry => [entry.mod_id, entry.display_order]));

        // Step 2: Fetch mod details from the mods table for the fetched mod_ids
        const { data: mods, error: modsError } = await supabaseClient
            .from('mods')
            .select('*')
            .in('id', modIds);

        if (modsError) {
            console.error('Error fetching mod details for suggested mods:', modsError);
            return null;
        }

        if (!mods) {
            console.log('No mod details found for the suggested mod IDs.');
            return [];
        }

        // Step 3: Sort the fetched mods based on the original display_order
        // The .in() filter does not guarantee order, so we re-sort.
        const sortedMods = mods.sort((a, b) => {
            const orderA = orderMap.get(a.id) ?? Infinity;
            const orderB = orderMap.get(b.id) ?? Infinity;
            return orderA - orderB;
        });

        // Cache the final sorted suggested mods
        try {
            const dataToCache = { timestamp: Date.now(), data: sortedMods };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Suggested mods data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error("Error storing suggested_mods data in localStorage:", e);
        }

        return sortedMods;

    } catch (error) {
        console.error('Unexpected error in fetchSuggestedModsFromSupabase:', error);
        return null;
    }
}

// Function to fetch new mods (uses admin settings for duration)
async function fetchNewModsFromSupabase(limit = null) {
    // Get admin settings
    const adminDuration = parseInt(localStorage.getItem('newModsDuration') || '7');
    const adminCacheMinutes = parseInt(localStorage.getItem('newModsCacheMinutes') || '3');
    const adminHomePageLimit = parseInt(localStorage.getItem('newModsHomePageLimit') || '10');

    // Use admin limit if no limit specified
    const effectiveLimit = limit || adminHomePageLimit;

    const cacheKey = `new_mods_${effectiveLimit}_${adminDuration}`;
    const cachedData = localStorage.getItem(cacheKey);

    // Check cache first (uses admin cache duration)
    if (cachedData) {
        try {
            const parsed = JSON.parse(cachedData);
            const cacheAge = Date.now() - parsed.timestamp;
            const cacheMaxAge = adminCacheMinutes * 60 * 1000; // Convert minutes to milliseconds

            if (cacheAge < cacheMaxAge) {
                console.log(`Using cached new mods data (age: ${Math.round(cacheAge / 1000)}s, duration: ${adminDuration} days)`);
                return parsed.data;
            }
        } catch (e) {
            console.error('Error parsing cached new mods data:', e);
        }
    }

    console.log(`Fetching fresh new mods data from Supabase (duration: ${adminDuration} days)`);
    try {
        // Calculate date based on admin duration
        const daysAgo = new Date();
        daysAgo.setDate(daysAgo.getDate() - adminDuration);
        const daysAgoISO = daysAgo.toISOString();

        let query = supabaseClient
            .from('mods')
            .select('*')
            .gte('created_at', daysAgoISO) // Greater than or equal to admin duration days ago
            .order('created_at', { ascending: false }); // Newest first

        if (limit && typeof limit === 'number' && limit > 0) {
            query = query.limit(limit);
        }

        const { data: newModsData, error } = await query;

        if (error) {
            console.error('Error fetching new mods from Supabase:', error);
            return null;
        }

        if (!newModsData || newModsData.length === 0) {
            console.log(`No new mods found in the last ${adminDuration} days.`);
            return [];
        }

        // Cache the new mods data
        try {
            const dataToCache = { timestamp: Date.now(), data: newModsData };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`New mods data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error('Error storing new mods data in localStorage:', e);
        }

        return newModsData;

    } catch (error) {
        console.error('Unexpected error in fetchNewModsFromSupabase:', error);
        return null;
    }
}

// Function to fetch Free Addons from Supabase
async function fetchFreeAddonsFromSupabase(limit = 20) {
    const cacheKey = `free_addons_${limit || 'none'}`;
    const cachedData = localStorage.getItem(cacheKey);

    // Check cache first
    if (cachedData) {
        try {
            const parsed = JSON.parse(cachedData);
            const cacheAge = Date.now() - parsed.timestamp;
            if (cacheAge < 300000) { // 5 minutes cache
                console.log(`Using cached Free Addons data (age: ${Math.round(cacheAge / 1000)}s)`);
                return parsed.data;
            }
        } catch (e) {
            console.error('Error parsing cached Free Addons data:', e);
        }
    }

    console.log('Fetching fresh Free Addons data from Supabase');
    try {
        // First, check if the free_addons table exists by trying a simple count query
        const { error: tableCheckError } = await supabaseClient
            .from('free_addons')
            .select('id', { count: 'exact', head: true })
            .limit(1);

        if (tableCheckError) {
            console.warn('Free Addons table not found or not accessible:', tableCheckError.message);
            // Return empty array instead of null to avoid breaking the UI
            return [];
        }

        // Now get the free_addons entries with mod details
        let query = supabaseClient
            .from('free_addons')
            .select(`
                *,
                mods (*)
            `)
            .eq('is_active', true)
            .order('display_order', { ascending: true });

        if (limit && typeof limit === 'number' && limit > 0) {
            query = query.limit(limit);
        }

        const { data: freeAddonsData, error } = await query;

        if (freeAddonsData) {
            freeAddonsData.forEach(item => {
                if (item.mods && (!item.mods.download_url || !item.mods.download_url.startsWith('http'))) {
                    console.warn(`[DataIssue] Free Addon Mod ID: ${item.mods.id}, Name: ${item.mods.name}, Missing or invalid download_url from Supabase fetch: '${item.mods.download_url}'.`);
                }
            });
        }

        if (error) {
            console.error('Error fetching Free Addons from Supabase:', error);
            return [];
        }

        if (!freeAddonsData || freeAddonsData.length === 0) {
            console.log('No Free Addons found in Supabase.');
            return [];
        }

        // Extract the mods data and sort by display_order
        const sortedMods = freeAddonsData
            .filter(item => item.mods) // Ensure mod data exists
            .map(item => item.mods)
            .slice(0, limit || 20); // Ensure we don't exceed the limit

        // Cache the final sorted Free Addons
        try {
            const dataToCache = { timestamp: Date.now(), data: sortedMods };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Free Addons data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error('Error storing Free Addons data in localStorage:', e);
        }

        return sortedMods;

    } catch (error) {
        console.error('Unexpected error in fetchFreeAddonsFromSupabase:', error);
        return null;
    }
}


async function displayModsBySection() {
    console.log("[DEBUG] displayModsBySection START - انسخة اجديدة");
    const newsSection = document.getElementById('news-section');
    const suggestedModsSection = document.getElementById('suggested-mods-section');
    const addonsSection = document.getElementById('addons-section');
    const texturePackSection = document.getElementById('texture-pack-section');
    const shadersSection = document.getElementById('shaders-section');
    const mapsSection = document.getElementById('maps-section');
    const seedsSection = document.getElementById('seeds-section');
    const singleCategoryContainer = document.getElementById('singleCategoryContainer');
    const sortButtons = document.getElementById('sortButtons');

    const newsContainer = document.getElementById('news-mods');
    const suggestedModsContainer = document.getElementById('suggested-mods');
    const addonsContainer = document.getElementById('addons-mods');
    const texturePackContainer = document.getElementById('texture-pack-mods');
    const shadersContainer = document.getElementById('shaders-mods');
    const mapsContainer = document.getElementById('maps-mods');
    const seedsContainer = document.getElementById('seeds-mods');

    // إخفاء حاوية افئة اواحدة وأزرار افرز
    if (singleCategoryContainer) singleCategoryContainer.style.display = 'none';
    if (sortButtons) {
        sortButtons.style.display = 'none';
    }

    // إظهار أقسام "All" باترتيب اجديد: News, Addons, Suggested, Shaders, Texture Pack, Seeds, Maps
    if (newsSection) newsSection.style.display = 'block'; // News - أو قسم
    if (addonsSection) addonsSection.style.display = 'block'; // Addons - اقسم اثاني (يتضمن Free Addons)
    if (suggestedModsSection) suggestedModsSection.style.display = 'block'; // Suggested - اقسم اثاث
    if (shadersSection) shadersSection.style.display = 'block'; // Shaders - اقسم ارابع
    if (texturePackSection) texturePackSection.style.display = 'block'; // Texture Pack - اقسم اخامس
    if (seedsSection) seedsSection.style.display = 'block'; // Seeds - اقسم اسادس
    if (mapsSection) mapsSection.style.display = 'block'; // Maps - اقسم اسابع

    // وضع مؤشر تحمي أقسام اجديدة
    [newsContainer, suggestedModsContainer, addonsContainer, shadersContainer, texturePackContainer,
     seedsContainer, mapsContainer].forEach(container => {
        if (container) {
            container.innerHTML = '<div class="loading-indicator" style="display:flex; justify-content:center; padding: 20px;"><div class="loading-spinner"></div> Loading...</div>';
        }
    });

    // جب ابيانات - ترتيب جديد: News, Addons (مع Free Addons), Suggested, Shaders, Texture Pack, Seeds, Maps
    const fetchPromises = [
        fetchNewModsFromSupabase(null),                        // News - امودات اجديدة (حسب إعدادات اأدمن)
        fetchModsFromSupabase('Addons', null, false, 10),      // Addons - اقسم اثاني
        fetchFreeAddonsFromSupabase(20),                       // Free Addons - سيتم دمجها مع Addons
        fetchSuggestedModsFromSupabase(10),                    // Suggested - اقسم اثاث
        fetchModsFromSupabase('Shaders', null, false, 10),     // Shaders - اقسم ارابع
        fetchModsFromSupabase('Texture', null, false, 10),     // Texture Packs - اقسم اخامس
        fetchModsFromSupabase('Seeds', null, false, 10),       // Seeds - اقسم اسادس
        fetchModsFromSupabase('Maps', null, false, 10),        // Maps - اقسم اسابع
        fetchBannerAds(),                                      // Banner Ads
        fetchFeaturedAddons()                                  // Featured Addons with special effects
    ];

    try {
        let [newsItems, addonsItems, freeAddonsItems, suggestedItems, shadersItems, textureItems, seedsItems, mapsItems, bannerAdsItems, featuredAddonsItems] = await Promise.all(fetchPromises);

        // Store banner ads in global variable and display them
        bannerAds = bannerAdsItems || [];
        displayBannerAds();

        // مسح مؤشرات اتحمي أقسام اجديدة
        [newsContainer, suggestedModsContainer, addonsContainer, shadersContainer, texturePackContainer,
         seedsContainer, mapsContainer].forEach(container => {
            if (container) container.innerHTML = '';
        });

        // دمج Free Addons مع Addons - Free Addons تظهر أواً
        if (freeAddonsItems && freeAddonsItems.length > 0) {
            // إضافة عامة free_addon مودات امجانية
            freeAddonsItems.forEach(item => {
                item.is_free_addon = true;
            });

            // دمج Free Addons مع Addons - Free Addons أواً
            if (addonsItems) {
                addonsItems = [...freeAddonsItems, ...addonsItems];
            } else {
                addonsItems = freeAddonsItems;
            }
        }

        // Shuffle for categories (no need to slice as we already limited to 10)
        if (shadersItems) {
            shadersItems = shuffleArray(shadersItems);
        }
        if (textureItems) {
            textureItems = shuffleArray(textureItems);
        }
        if (seedsItems) {
            seedsItems = shuffleArray(seedsItems);
        }
        if (mapsItems) {
            mapsItems = shuffleArray(mapsItems);
        }
        // ا نخط Addons أن Free Addons يجب أن تبقى في امقدمة
        // if (addonsItems) {
        //     addonsItems = shuffleArray(addonsItems);
        // }

        // Create a map of featured addon IDs for quick lookup
        const featuredAddonIds = new Set();
        if (featuredAddonsItems && featuredAddonsItems.length > 0) {
            featuredAddonsItems.forEach(item => {
                featuredAddonIds.add(item.id);
            });
        }


        // Function to add animated orbs to featured addons
        function addAnimatedOrbs(element) {
            // Create 5 orbs with random positions
            for (let i = 0; i < 5; i++) {
                const orb = document.createElement('div');
                orb.className = 'orb';

                // Random position within the element
                const leftPos = Math.random() * 100; // Random percentage across width
                orb.style.left = `${leftPos}%`;
                orb.style.bottom = '0';

                // Random animation delay and duration for more natural effect
                const delay = Math.random() * 2; // 0-2s delay
                const duration = 2 + Math.random() * 2; // 2-4s duration

                orb.style.animation = `orbFloat ${duration}s ease-in-out ${delay}s infinite`;

                element.appendChild(orb);
            }
        }

        // داة مساعدة مء احاويات
        const populateContainer = (container, items, categoryName, sectionElement) => {
            if (!container) return;

            if (items === null) {
                container.innerHTML = `<p style="text-align: center; padding: 20px; color: #f87171;">Error fetching ${categoryName}.</p>`;
                if (sectionElement) sectionElement.classList.remove('hidden-section'); // Ensure visible if error
            } else if (items.length === 0) {
                // If no items, hide the entire section
                if (sectionElement) {
                    sectionElement.classList.add('hidden-section');
                    console.log(`[DEBUG] Hiding section: ${categoryName} (no mods)`);
                }
                container.innerHTML = ''; // Clear any loading indicators or "No mods" message
            } else {
                if (sectionElement) sectionElement.classList.remove('hidden-section'); // Ensure visible if items exist
                items.forEach(item => {
                    const modElement = createModElement(item, 'item'); // 'item' نمط اأفقي

                    // إضافة فئة popular-mod للمودات الشعبية
                    if (isModPopular && isModPopular(item)) {
                        modElement.classList.add('popular-mod');
                    }

                    container.appendChild(modElement);
                });
            }
        };

        // Function to populate Free Addons container (special effects removed, now generic)
        const populateFreeAddonsContainer = (container, items, categoryName) => {
            if (!container) return;
            if (items === null) {
                container.innerHTML = `<p style="text-align: center; padding: 20px; color: #f87171;">Error fetching ${categoryName}.</p>`;
            } else if (items.length === 0) {
                container.innerHTML = `<p style="text-align: center; padding: 20px;">No ${categoryName} mods to display.</p>`;
            } else {
                items.forEach(item => {
                    const modElement = createModElement(item, 'item'); // 'item' for horizontal style

                    // إضافة class free-addon-mod تطبيق تأثير امعان اأبيض
                    modElement.classList.add('free-addon-mod');
                    
                    // إضافة فئة popular-mod للمودات الشعبية
                    if (isModPopular && isModPopular(item)) {
                        modElement.classList.add('popular-mod');
                    }

                    container.appendChild(modElement);
                });
            }
        };

        // Function to add floating orbs animation
        const addFloatingOrbs = (element) => {
            setInterval(() => {
                if (Math.random() < 0.3) { // 30% chance every interval
                    const orb = document.createElement('div');
                    orb.className = 'floating-orb';

                    // Random position within the element
                    orb.style.left = Math.random() * (element.offsetWidth - 10) + 'px';
                    orb.style.bottom = '0px';

                    element.appendChild(orb);

                    // Remove orb after animation completes
                    setTimeout(() => {
                        if (orb.parentNode) {
                            orb.parentNode.removeChild(orb);
                        }
                    }, 3000);
                }
            }, 2000); // Check every 2 seconds
        };

        // Function to add sparkle effects

        // Function to add pixel particles for Free Addons
        const addFreeAddonPixelParticles = (element) => {
            setInterval(() => {
                if (Math.random() < 0.7) { // 70% chance every interval for more particles
                    const particle = document.createElement('div');
                    particle.className = 'free-addon-pixel-particle';

                    // Random position within the element
                    const leftPos = Math.random() * (element.offsetWidth - 4);
                    const bottomPos = Math.random() * 20; // Start from bottom area

                    particle.style.left = leftPos + 'px';
                    particle.style.bottom = bottomPos + 'px';

                    element.appendChild(particle);

                    // Remove particle after animation completes
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 3000);
                }
            }, 700); // Check every 0.7 seconds for more frequent particles
        };

        // مء ك قسم
        const containersToAnimate = [];
        if (newsContainer) containersToAnimate.push(newsContainer);
        if (suggestedModsContainer) containersToAnimate.push(suggestedModsContainer);
        if (addonsContainer) containersToAnimate.push(addonsContainer);
        if (texturePackContainer) containersToAnimate.push(texturePackContainer);
        if (shadersContainer) containersToAnimate.push(shadersContainer);
        if (seedsContainer) containersToAnimate.push(seedsContainer);
        if (mapsContainer) containersToAnimate.push(mapsContainer);

        containersToAnimate.forEach(container => {
            container.classList.remove('animate-visible'); // Reset animation
            container.classList.add('initial-hidden-animate');
        });

        // مء احاويات باترتيب اجديد: News, Addons (مع Free Addons), Suggested, Shaders, Texture Pack, Seeds, Maps
        populateContainer(newsContainer, newsItems, 'News', newsSection); // News - أو قسم
        populateContainer(addonsContainer, addonsItems, 'Addons', addonsSection); // Addons - اقسم اثاني (يتضمن Free Addons)
        populateContainer(suggestedModsContainer, suggestedItems, 'Suggested', suggestedModsSection); // Suggested - اقسم اثاث
        populateContainer(shadersContainer, shadersItems, 'Shaders', shadersSection); // Shaders - اقسم ارابع
        populateContainer(texturePackContainer, textureItems, 'Texture', texturePackSection); // Texture Pack - اقسم اخامس
        populateContainer(seedsContainer, seedsItems, 'Seeds', seedsSection); // Seeds - اقسم اسادس
        populateContainer(mapsContainer, mapsItems, 'Maps', mapsSection); // Maps - اقسم اسابع

        // تهيئة اتحمي اكسو صور اجديدة
        initializeLazyLoading('.lazy-load');

        // Trigger animation for each container after a short delay
        containersToAnimate.forEach(container => {
            setTimeout(() => {
                if (container.classList.contains('initial-hidden-animate')) {
                    container.classList.add('animate-visible');
                }
            }, 50);
        });

        // Insert custom sections after all default sections are loaded
        if (window.customSectionsManager) {
            setTimeout(async () => {
                try {
                    await window.customSectionsManager.insertCustomSectionsIntoAll();
                    console.log("[DEBUG] Custom sections inserted successfully");
                } catch (error) {
                    console.error("[DEBUG] Error inserting custom sections:", error);
                }
            }, 500); // Small delay to ensure sections are rendered first
        }

    } catch (error) {
        console.error("[DEBUG] Error in displayModsBySection (modified):", error);
        const errorMsg = '<p style="text-align: center; padding: 20px; color: #f87171;">Error loading mods.</p>';
        if (newsContainer) newsContainer.innerHTML = errorMsg;
        if (suggestedModsContainer) suggestedModsContainer.innerHTML = errorMsg;
        if (addonsContainer) addonsContainer.innerHTML = errorMsg;
        if (shadersContainer) shadersContainer.innerHTML = errorMsg;
        if (texturePackContainer) texturePackContainer.innerHTML = errorMsg;
        if (seedsContainer) seedsContainer.innerHTML = errorMsg;
        if (mapsContainer) mapsContainer.innerHTML = errorMsg;
    }
    console.log("[DEBUG] displayModsBySection END - انسخة امعدة");
}

// Function to display mods for a single category (vertical list)
async function displaySingleCategory(category, sortBy = currentSortBy, ascending = currentSortAscending) {
    console.log(`Displaying single category: ${category}, Sort: ${sortBy}, Asc: ${ascending}`);
    const newsSection = document.getElementById('news-section');
    const suggestedModsSection = document.getElementById('suggested-mods-section');
    const addonsSection = document.getElementById('addons-section');
    const texturePackSection = document.getElementById('texture-pack-section');
    const shadersSection = document.getElementById('shaders-section');
    const mapsSection = document.getElementById('maps-section');
    const seedsSection = document.getElementById('seeds-section');
    const singleCategoryContainer = document.getElementById('singleCategoryContainer');
    const sortButtons = document.getElementById('sortButtons');

    // Hide section containers, show single category container and sort buttons
    if (newsSection) newsSection.style.display = 'none';
    if (suggestedModsSection) suggestedModsSection.style.display = 'none';
    if (addonsSection) addonsSection.style.display = 'none';
    if (shadersSection) shadersSection.style.display = 'none';
    if (texturePackSection) texturePackSection.style.display = 'none';
    if (seedsSection) seedsSection.style.display = 'none';
    if (mapsSection) mapsSection.style.display = 'none';
    if (singleCategoryContainer) singleCategoryContainer.style.display = 'block';

    if (sortButtons) {
        // For "Suggested" category, we might not want to show all sort buttons,
        // as it's pre-sorted by admin. For now, show them for all.
        sortButtons.style.display = 'flex';
        sortButtons.classList.remove('animate-visible');
        sortButtons.classList.add('initial-hidden-animate');
        setTimeout(() => {
            if (sortButtons.classList.contains('initial-hidden-animate')) {
                sortButtons.classList.add('animate-visible');
            }
        }, 50);
    }

    if (singleCategoryContainer) {
        singleCategoryContainer.innerHTML = '<div class="loading-indicator" style="display:flex; justify-content:center; padding: 20px;"><div class="loading-spinner"></div></div>';
    }

    let items;
    if (category === 'All') {
        // When 'All' category is selected for sorting, fetch all mods with the specified sort order
        items = await fetchModsFromSupabase('All', sortBy, ascending, null); // Pass 'All' category to fetch all mods
        console.log(`Fetched ${items ? items.length : 0} mods for All category with sort: ${sortBy}, asc: ${ascending}`);
    } else if (category === 'News') {
        // Fetch all new mods from the last 7 days, sorted from newest to oldest
        items = await fetchNewModsFromSupabase(null); // null limit to fetch all new mods
        console.log(`Fetched ${items ? items.length : 0} new mods for News category`);
    } else if (category === 'Suggested') {
        // Fetch all suggested mods (or a larger limit if pagination is desired later)
        // For "see all Suggested", we sort by display_order. Other sort buttons might not be relevant.
        items = await fetchSuggestedModsFromSupabase(null); // null limit to fetch all, or a larger number like 100
    } else if (category === 'Addons') {
        // For Addons category, fetch both regular addons and free addons, then merge them
        const [regularAddons, freeAddons] = await Promise.all([
            fetchModsFromSupabase('Addons', sortBy, ascending),
            fetchFreeAddonsFromSupabase(null) // Fetch all free addons
        ]);

        // Mark free addons and merge with regular addons - Free Addons أواً
        if (freeAddons && freeAddons.length > 0) {
            freeAddons.forEach(item => {
                item.is_free_addon = true;
            });
            items = [...(freeAddons || []), ...(regularAddons || [])];
        } else {
            items = regularAddons;
        }
    } else {
        items = await fetchModsFromSupabase(category, sortBy, ascending);
    }

    if (singleCategoryContainer) singleCategoryContainer.innerHTML = '';

    if (items === null) {
        if (singleCategoryContainer) singleCategoryContainer.innerHTML = '<p style="text-align: center; padding: 20px; color: #f87171;">Error fetching data.</p>';
        return;
    }
    if (items.length === 0) {
        if (singleCategoryContainer) singleCategoryContainer.innerHTML = `<p style="text-align: center; padding: 20px;">No ${category} mods to display.</p>`;
        return;
    }

    // Removed assignment to displayedModsData

    // Apply initial hidden class
    if (singleCategoryContainer) {
        singleCategoryContainer.classList.remove('animate-visible'); // Reset animation
        singleCategoryContainer.classList.add('initial-hidden-animate');
    }

    // If this is the Addons category, fetch featured addons to apply special effects
    let featuredAddonIds = new Set();
    if (category === 'Addons') {
        try {
            const featuredAddons = await fetchFeaturedAddons();
            if (featuredAddons && featuredAddons.length > 0) {
                featuredAddons.forEach(addon => {
                    featuredAddonIds.add(addon.id);
                });
            }
        } catch (error) {
            console.error('Error fetching featured addons for single category view:', error);
        }
    }

    items.forEach(item => {
        // Use 'mod-card' style for vertical list items
        const modElement = createModElement(item, 'mod-card');

        // If this is the Addons category and the item is in the featured list
        if (category === 'Addons' && featuredAddonIds.has(item.id)) {
            // Add the featured class for styling
            modElement.classList.add('featured-addon');

            // Add animated orbs
            addAnimatedOrbs(modElement);
        }

        // إضافة تأثيرات مودات اشعبية (نظام ديناميكي)
        if (isModPopular(item)) {
            modElement.classList.add('popular-mod');
            addPixelParticlesGlobal(modElement);

            // إضافة أيقونة Popular في اصورة
            const imageContainer = modElement.querySelector('.mod-image-container');
            if (imageContainer) {
                const popularIcon = document.createElement('div');
                popularIcon.className = 'popular-icon';
                popularIcon.textContent = 'POPULAR';
                imageContainer.appendChild(popularIcon);
            }
        }

        // إضافة تأثيرات خاصة مودات Free Addons
        if (item.is_free_addon) {
            modElement.classList.add('free-addon-mod');
            addFreeAddonPixelParticlesGlobal(modElement);
        }

        if (singleCategoryContainer) singleCategoryContainer.appendChild(modElement);
    });

    // Trigger animation after a short delay
    if (singleCategoryContainer) {
        setTimeout(() => {
            if (singleCategoryContainer.classList.contains('initial-hidden-animate')) { // Ensure it's still meant to be animated
                singleCategoryContainer.classList.add('animate-visible');
            }
        }, 50); // Small delay to ensure elements are rendered
    }
    // applyFadeInAnimation('#singleCategoryContainer .mod-card'); // Removed old animation call
    initializeLazyLoading('#singleCategoryContainer .lazy-load'); // Initialize lazy loading for new elements
}

// نظام ديناميكي تحديد معايير اشعبية
let popularityThresholds = {
    downloads: 1000,
    likes: 50,
    lastUpdated: null
};

// داة حساب معايير اشعبية بشك ديناميكي
async function calculateDynamicPopularityThresholds() {
    try {
        console.log("Calculating dynamic popularity thresholds...");

        // جب إحصائيات عامة من قاعدة ابيانات
        const { data: stats, error } = await supabaseClient
            .from('mods')
            .select('downloads, likes')
            .not('downloads', 'is', null)
            .not('likes', 'is', null);

        if (error) {
            console.error('Error fetching mod statistics:', error);
            return popularityThresholds; // استخدام اقيم اافتراضية
        }

        if (!stats || stats.length === 0) {
            console.log("No mod statistics found, using default thresholds");
            return popularityThresholds;
        }

        // حساب امتوسطات وامئينات
        const downloads = stats.map(mod => mod.downloads || 0).sort((a, b) => b - a);
        const likes = stats.map(mod => mod.likes || 0).sort((a, b) => b - a);

        // حساب امئين 80 (أعى 20% من امودات)
        const downloadsP80 = downloads[Math.floor(downloads.length * 0.2)] || 1000;
        const likesP80 = likes[Math.floor(likes.length * 0.2)] || 50;

        // تطبيق حد أدنى وأقصى معايير
        const newDownloadsThreshold = Math.max(500, Math.min(downloadsP80, 10000));
        const newLikesThreshold = Math.max(25, Math.min(likesP80, 1000));

        popularityThresholds = {
            downloads: newDownloadsThreshold,
            likes: newLikesThreshold,
            lastUpdated: new Date().toISOString()
        };

        console.log("Updated popularity thresholds:", popularityThresholds);

        // حفظ امعايير في localStorage استخدام اسريع
        localStorage.setItem('popularityThresholds', JSON.stringify(popularityThresholds));

        return popularityThresholds;

    } catch (error) {
        console.error('Error calculating dynamic popularity thresholds:', error);
        return popularityThresholds; // استخدام اقيم اافتراضية
    }
}

// داة تحقق من شعبية امود
function isModPopular(mod) {
    const downloads = mod.downloads || 0;
    const likes = mod.likes || 0;

    // امود شعبي إذا تجاوز أي من امعايير
    return downloads >= popularityThresholds.downloads || likes >= popularityThresholds.likes;
}

// داة تحقق من كون امود جديد
function isModNew(mod) {
    if (!mod.created_at) return false;

    // احصو عى مدة اعرض من إعدادات اأدمن (افتراضي 7 أيام)
    const adminDuration = parseInt(localStorage.getItem('newModsDuration') || '7');

    const createdDate = new Date(mod.created_at);
    const now = new Date();
    const daysDiff = (now - createdDate) / (1000 * 60 * 60 * 24);

    return daysDiff <= adminDuration;
}

// تحديث امعايير عند بدء اتطبيق
async function initializePopularitySystem() {
    // محاوة تحمي امعايير امحفوظة
    const savedThresholds = localStorage.getItem('popularityThresholds');
    if (savedThresholds) {
        try {
            const parsed = JSON.parse(savedThresholds);
            const lastUpdated = new Date(parsed.lastUpdated);
            const now = new Date();
            const hoursSinceUpdate = (now - lastUpdated) / (1000 * 60 * 60);

            // تحديث امعايير ك 24 ساعة
            if (hoursSinceUpdate < 24) {
                popularityThresholds = parsed;
                console.log("Using cached popularity thresholds:", popularityThresholds);
                return;
            }
        } catch (error) {
            console.error('Error parsing saved thresholds:', error);
        }
    }

    // حساب معايير جديدة
    await calculateDynamicPopularityThresholds();
}

// Global functions for Free Addons effects (used in both section and single category views)
function addFloatingOrbsToElement(element) {
    setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance every interval
            const orb = document.createElement('div');
            orb.className = 'floating-orb';

            // Random position within the element
            orb.style.left = Math.random() * (element.offsetWidth - 10) + 'px';
            orb.style.bottom = '0px';

            element.appendChild(orb);

            // Remove orb after animation completes
            setTimeout(() => {
                if (orb.parentNode) {
                    orb.parentNode.removeChild(orb);
                }
            }, 3000);
        }
    }, 2000); // Check every 2 seconds
}

// Global function for popular mod pixel particles (used in both section and single category views)
function addPixelParticlesGlobal(element) {
    // Check if the element contains a popular icon
    const hasPopularIcon = element.querySelector('.popular-icon');
    if (!hasPopularIcon) {
        return; // Do not add particles if no popular icon is present
    }

    setInterval(() => {
        if (Math.random() < 0.6) { // 60% chance every interval
            const particle = document.createElement('div');
            particle.className = 'pixel-particle';

            // Random position within the element
            const leftPos = Math.random() * (element.offsetWidth - 4);
            const bottomPos = Math.random() * 20; // Start from bottom area

            particle.style.left = leftPos + 'px';
            particle.style.bottom = bottomPos + 'px';

            element.appendChild(particle);

            // Remove particle after animation completes
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 3000);
        }
    }, 800); // Check every 0.8 seconds for more frequent particles
}

// Global function for Free Addons pixel particles (used in both section and single category views)
function addFreeAddonPixelParticlesGlobal(element) {
    // Check if the element contains a free addon icon
    const hasFreeAddonIcon = element.querySelector('.free-addon-icon');
    if (!hasFreeAddonIcon) {
        return; // Do not add particles if no free addon icon is present
    }

    setInterval(() => {
        if (Math.random() < 0.7) { // 70% chance every interval for more particles
            const particle = document.createElement('div');
            particle.className = 'free-addon-pixel-particle';

            // Random position within the element
            const leftPos = Math.random() * (element.offsetWidth - 4);
            const bottomPos = Math.random() * 20; // Start from bottom area

            particle.style.left = leftPos + 'px';
            particle.style.bottom = bottomPos + 'px';

            element.appendChild(particle);

            // Remove particle after animation completes
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 3000);
        }
    }, 700); // Check every 0.7 seconds for more frequent particles
}

function addSparkleEffectsToElement(element) {
    setInterval(() => {
        if (Math.random() < 0.4) { // 40% chance every interval
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';

            // Random position within the element
            sparkle.style.left = Math.random() * element.offsetWidth + 'px';
            sparkle.style.top = Math.random() * element.offsetHeight + 'px';

            element.appendChild(sparkle);

            // Remove sparkle after animation completes
            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 2000);
        }
    }, 1500); // Check every 1.5 seconds
}


// --- Modal Display ---
let currentModalModId = null;

// Function to increment clicks via direct UPDATE (إصاح مشكة RPC)
async function incrementModClicks(modId) {
    if (!modId) return;
    console.log(`Incrementing clicks for mod ID: ${modId}`);

    // اتحقق من وجود supabaseClient
    if (!supabaseClient) {
        console.warn('Supabase client not available, skipping click increment');
        return;
    }

    try {
        // احصو عى اعدد احاي أواً ثم تحديثه
        const { data: currentData, error: fetchError } = await supabaseClient
            .from('mods')
            .select('clicks')
            .eq('id', modId)
            .single();

        if (fetchError) {
            console.error(`Error fetching current clicks for mod ${modId}:`, fetchError);
            return;
        }

        // حساب اعدد اجديد
        const currentClicks = currentData?.clicks || 0;
        const newClickCount = currentClicks + 1;

        // تحديث اعدد في قاعدة ابيانات
        const { error: updateError } = await supabaseClient
            .from('mods')
            .update({
                clicks: newClickCount,
                updated_at: new Date().toISOString()
            })
            .eq('id', modId);

        if (updateError) {
            console.error(`Error updating clicks for mod ${modId}:`, updateError);
        } else {
            console.log(`✅ Successfully incremented clicks for mod ${modId} from ${currentClicks} to ${newClickCount}.`);
        }
    } catch (error) {
        console.error(`💥 Unexpected error incrementing clicks for mod ${modId}:`, error);

        // ا نوقف اتطبيق بسبب خطأ في اإحصائيات
        console.warn('🔄 Continuing app execution despite click tracking error');
    }
}

function showShaderPatchWarningDialog(item, proceedCallback) {
    const warningDismissedKey = 'shader_patch_warning_dismissed';
    if (localStorage.getItem(warningDismissedKey) === 'true') {
        if (typeof proceedCallback === 'function') {
            proceedCallback(item);
        }
        return;
    }

    const existingDialog = document.getElementById('shader-patch-warning-overlay');
    if (existingDialog) existingDialog.remove();

    const overlay = document.createElement('div');
    overlay.id = 'shader-patch-warning-overlay';
    // انسخ أنماط CSS اازمة ـ .shader-warning-overlay و .shader-warning-dialog من انسخة احاية
    // أو أنشئها. مثا أنماط اأساسية:
    overlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.7); display: flex;
        justify-content: center; align-items: center; z-index: 100007; /* أعى من اإعانات */
        padding: 20px; box-sizing: border-box;
    `;

    const dialog = document.createElement('div');
    dialog.className = 'shader-warning-dialog'; // ستحتاج تعريف هذا اكاس في CSS
    dialog.style.cssText = `
        background-color: #333; color: white; padding: 20px; border-radius: 10px;
        width: 100%; max-width: 350px; text-align: center;
        border: 2px solid #ffcc00; /* Changed to yellow */
        /* box-shadow: 0 0 15px rgba(255, 165, 0, 0.5); */ /* تجاه اظ مبدئيًا */
    `;

    const icon = document.createElement('img');
    icon.src = 'image/mineraft_patch.jpg'; // تأكد من صحة امسار
    icon.alt = 'Important';
    icon.style.cssText = `width: 100%; aspect-ratio: 16 / 9; max-height: 200px; object-fit: cover; margin-bottom: 15px; border-radius: 8px; border: 3px solid #ffcc00;`; /* Yellow border, 16:9 aspect ratio, larger */

    const title = document.createElement('h2');
    title.textContent = t('shader_warning_title');
    title.style.cssText = `color: orange; margin-bottom: 10px; font-size: 1.4em;`;

    const description = document.createElement('p');
    description.textContent = t('shader_warning_description');
    description.style.cssText = `margin-bottom: 20px; font-size: 0.95em; line-height: 1.5;`;

    const translationButtonsDiv = document.createElement('div');
    translationButtonsDiv.style.cssText = `margin-bottom: 20px; display: flex; justify-content: center; gap: 10px;`;

    const translateArButton = document.createElement('button');
    translateArButton.textContent = t('shader_warning_translate_ar');

    const translateEnButton = document.createElement('button'); // تعريفها هنا
    translateEnButton.textContent = t('shader_warning_translate_en');

    // ... (انسخ بقية محتوى وخصائص أزرار اترجمة من انسخة احاية)
    // أنماط أساسية أزرار:
    [translateArButton, translateEnButton].forEach(btn => {
        btn.style.cssText = `
            background-color: #ffcc00; color: black; border: 1px solid #cc9900; /* Yellow background, black text, darker yellow border */
            padding: 8px 12px; border-radius: 5px; cursor: pointer; font-family: 'VT323', monospace; /* Pixel font */
        `;
        // btn.classList.add('active'); // أحد اأزرار
    });
    // ... (نسخ منطق setLanguage وربط اأحداث أزرار اترجمة)

    const actionButtonsDiv = document.createElement('div');
    actionButtonsDiv.style.cssText = `display: flex; flex-direction: column; gap: 10px;`;

    const doneButton = document.createElement('button');
    doneButton.textContent = t('shader_warning_understand');

    const dismissButton = document.createElement('button'); // تعريفها هنا
    dismissButton.textContent = t('shader_warning_dont_show');
    // ... (انسخ خصائص doneButton و dismissButton من انسخة احاية)
    // أنماط أساسية أزرار:
    [doneButton, dismissButton].forEach(btn => {
        btn.style.cssText = `
            background-color: #ffcc00; color: black; border: none; /* Yellow background, black text */
            padding: 10px 15px; border-radius: 5px; cursor: pointer; font-weight: bold; font-family: 'VT323', monospace; /* Pixel font */
        `;
    });
    // ... (نسخ ربط اأحداث ـ doneButton و dismissButton)

    // Language switching functionality using the global translation system
    const updateDialogLanguage = () => {
        const currentLang = translationManager.getCurrentLanguage();
        title.textContent = t('shader_warning_title');
        description.textContent = t('shader_warning_description');
        translateArButton.textContent = t('shader_warning_translate_ar');
        translateEnButton.textContent = t('shader_warning_translate_en');
        doneButton.textContent = t('shader_warning_understand');
        dismissButton.textContent = t('shader_warning_dont_show');

        // Update button styles based on current language
        if (currentLang === 'ar') {
            translateArButton.style.backgroundColor = 'orange';
            translateEnButton.style.backgroundColor = '#555';
            dialog.setAttribute('dir', 'rtl');
        } else {
            translateEnButton.style.backgroundColor = 'orange';
            translateArButton.style.backgroundColor = '#555';
            dialog.setAttribute('dir', 'ltr');
        }
    };

    translateArButton.onclick = () => {
        translationManager.setLanguage('ar');
        updateDialogLanguage();
    };
    translateEnButton.onclick = () => {
        translationManager.setLanguage('en');
        updateDialogLanguage();
    };

    // Initialize with current language
    updateDialogLanguage();

    doneButton.onclick = () => {
        overlay.remove(); document.body.style.overflow = '';
        if (typeof proceedCallback === 'function') proceedCallback(item);
    };
    dismissButton.onclick = () => {
        localStorage.setItem(warningDismissedKey, 'true');
        overlay.remove(); document.body.style.overflow = '';
        if (typeof proceedCallback === 'function') proceedCallback(item);
    };
    // --- نهاية نسخ منطق اترجمة ---


    translationButtonsDiv.appendChild(translateArButton);
    translationButtonsDiv.appendChild(translateEnButton);
    actionButtonsDiv.appendChild(doneButton);
    actionButtonsDiv.appendChild(dismissButton);

    dialog.appendChild(icon);
    dialog.appendChild(title);
    dialog.appendChild(description);
    dialog.appendChild(translationButtonsDiv);
    dialog.appendChild(actionButtonsDiv);
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';
}

// فحص امربعات امخصصة
async function checkCustomDialog(item, callback) {
    try {
        // اتحقق من وجود مربع مخصص هذا امود
        const { data: dialogMods, error } = await supabaseClient
            .from('custom_dialog_mods')
            .select(`
                dialog_id,
                custom_mod_dialogs (
                    id,
                    title,
                    title_en,
                    description,
                    description_en,
                    image_url,
                    button_text,
                    button_text_en,
                    show_dont_show_again,
                    is_active
                )
            `)
            .eq('mod_id', item.id);

        if (error) {
            console.error('خطأ في فحص امربعات امخصصة:', error);
            callback(item); // متابعة بدون مربع مخصص
            return;
        }

        // ابحث عن مربع مفع
        const activeDialog = dialogMods?.find(dm =>
            dm.custom_mod_dialogs && dm.custom_mod_dialogs.is_active
        );

        if (activeDialog && activeDialog.custom_mod_dialogs) {
            const dialog = activeDialog.custom_mod_dialogs;

            // اتحقق من إعداد "عدم اظهور مجدداً"
            const dontShowKey = `custom_dialog_${dialog.id}_dont_show`;
            if (localStorage.getItem(dontShowKey) === 'true') {
                callback(item); // تم اختيار عدم اظهور مجدداً
                return;
            }

            // عرض امربع امخصص
            showCustomDialog(dialog, item, callback);
        } else {
            // ا يوجد مربع مخصص، متابعة عادية
            callback(item);
        }

    } catch (error) {
        console.error('خطأ في فحص امربعات امخصصة:', error);
        callback(item); // متابعة بدون مربع مخصص
    }
}

// عرض امربع امخصص
function showCustomDialog(dialog, item, callback) {
    // احصو عى غة امستخدم
    const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
    const isArabic = userLanguage === 'ar';

    console.log(`🌍 Showing custom dialog in language: ${userLanguage}`);

    // إنشاء overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100000;
        animation: fadeIn 0.3s ease;
    `;

    // إنشاء محتوى امربع
    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
        background: #000000;
        border-radius: 20px;
        padding: 30px;
        max-width: 500px;
        width: 90%;
        text-align: center;
        border: 3px solid #ffcc00;
        box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
        animation: slideInUp 0.4s ease;
        position: relative;
        direction: ${isArabic ? 'rtl' : 'ltr'};
    `;

    // تحديد انصوص بناءً عى اغة
    let dialogTitle, dialogDescription, dialogButtonText, dontShowAgainText;

    if (isArabic) {
        dialogTitle = dialog.title || dialog.title_en || 'عنوان غير محدد';
        dialogDescription = dialog.description || dialog.description_en || '';
        dialogButtonText = dialog.button_text || dialog.button_text_en || 'تم';
        dontShowAgainText = 'عدم اظهور مجدداً';
    } else {
        dialogTitle = dialog.title_en || dialog.title || 'No title specified';
        dialogDescription = dialog.description_en || dialog.description || '';
        dialogButtonText = dialog.button_text_en || dialog.button_text || 'OK';
        dontShowAgainText = "Don't show again";
    }

    // إنشاء محتوى HTML
    let contentHTML = '';

    // إضافة اصورة إذا كانت موجودة
    if (dialog.image_url) {
        contentHTML += `
            <img src="${dialog.image_url}" alt="${dialogTitle}"
                 style="max-width: 100%; max-height: 200px; border-radius: 10px; margin-bottom: 20px;"
                 onerror="this.style.display='none'">
        `;
    }

    // إضافة اعنوان
    contentHTML += `
        <h2 style="color: #ffffff; font-size: 1.5rem; font-weight: bold; margin-bottom: 15px; font-family: Arial, sans-serif;">
            ${escapeHtml(dialogTitle)}
        </h2>
    `;

    // إضافة اوصف إذا كان موجود
    if (dialogDescription) {
        contentHTML += `
            <p style="color: #ffffff; margin-bottom: 20px; line-height: 1.6; font-family: Arial, sans-serif;">
                ${escapeHtml(dialogDescription)}
            </p>
        `;
    }

    // إضافة اأزرار
    contentHTML += `
        <div style="display: flex; gap: 15px; justify-content: center; margin-bottom: 20px;">
            <button id="customDialogOkBtn" style="
                background: linear-gradient(45deg, #ffcc00, #ff9800);
                color: #000;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 1rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
                font-family: Arial, sans-serif;
            ">
                ${escapeHtml(dialogButtonText)}
            </button>
        </div>
    `;

    // إضافة خيار "عدم اظهور مجدداً" إذا كان مفع
    if (dialog.show_dont_show_again) {
        contentHTML += `
            <div style="margin-top: 15px;">
                <label style="color: #ffffff; font-size: 0.9rem; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 8px; font-family: Arial, sans-serif;">
                    <input type="checkbox" id="dontShowAgainCheckbox" style="margin: 0;">
                    ${dontShowAgainText}
                </label>
            </div>
        `;
    }

    // زر اإغاق
    contentHTML += `
        <button id="customDialogCloseBtn" style="
            position: absolute;
            top: 15px;
            ${isArabic ? 'left' : 'right'}: 15px;
            background: transparent;
            border: none;
            color: #ffffff;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
            font-family: Arial, sans-serif;
        ">&times;</button>
    `;

    dialogContent.innerHTML = contentHTML;
    overlay.appendChild(dialogContent);
    document.body.appendChild(overlay);

    // منع اتمرير في اخفية
    document.body.style.overflow = 'hidden';

    // إضافة مستمعي اأحداث
    const okBtn = document.getElementById('customDialogOkBtn');
    const closeBtn = document.getElementById('customDialogCloseBtn');
    const dontShowCheckbox = document.getElementById('dontShowAgainCheckbox');

    function closeDialog() {
        // حفظ إعداد "عدم اظهور مجدداً" إذا تم تحديده
        if (dontShowCheckbox && dontShowCheckbox.checked) {
            localStorage.setItem(`custom_dialog_${dialog.id}_dont_show`, 'true');
        }

        // إزاة امربع
        overlay.remove();
        document.body.style.overflow = '';

        // متابعة عرض تفاصي امود
        callback(item);
    }

    okBtn.addEventListener('click', closeDialog);
    closeBtn.addEventListener('click', closeDialog);

    // إغاق عند انقر خارج امربع
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeDialog();
        }
    });

    // تأثيرات hover أزرار
    okBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 4px 15px rgba(255, 204, 0, 0.4)';
    });

    okBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'none';
    });

    closeBtn.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'rgba(255, 204, 0, 0.2)';
    });

    closeBtn.addEventListener('mouseleave', function() {
        this.style.backgroundColor = 'transparent';
    });
}

// داة مساعدة تأمين انص من HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showModal(item) {
    console.log("[DEBUG] showModal called for item:", item);

    // >>> إضافة اتحقق من امربع امخصص هنا <<<
    checkCustomDialog(item, (confirmedItem) => {
        // >>> إضافة اتحقق من اشادر هنا <<<
        if (confirmedItem.category === 'Shaders' || confirmedItem.category === 'shaders') { // تحقق من اسم افئة بدقة
            showShaderPatchWarningDialog(confirmedItem, (shaderConfirmedItem) => {
                // هذا اكود سيتم تنفيذه بعد أن يضغط امستخدم "تم" أو "عدم اظهور مجددًا"
                // استمر في عرض اـ modal كامعتاد
                displayModalContent(shaderConfirmedItem); // استدعاء داة جديدة أو باقي كود showModal
            });
            return; // ا تعرض اـ modal مباشرة، انتظر نتيجة اتحذير
        }
        // >>> نهاية إضافة اتحقق <<<

        // إذا م يكن شادر، أو إذا تم تأكيد اتحذير، اعرض اـ modal
        displayModalContent(confirmedItem);
    });
}

// أنشئ داة جديدة أو انق باقي محتوى showModal اأصي إيها
function displayModalContent(item) {
    const modal = document.getElementById('modal');
    const modalContent = document.getElementById('modalContent');

    if (!modal || !modalContent) {
        console.error("Modal or ModalContent element not found!");
        return;
    }

    if (!item || !item.id) {
        console.error("Invalid item data for modal or incrementing clicks.");
        return;
    }

    incrementModClicks(item.id); // <<< أضف هذا ااستدعاء هنا

    currentModalModId = item.id; // يجب أن يكون هذا اسطر موجودًا
    let mainImageIndex = 0;

    // --- Define isDownloaded based on localStorage ---
    const isDownloaded = localStorage.getItem(`downloaded_${item.id}`) === 'true';
    console.log(`showModal: Mod ${item.id} isDownloaded status: ${isDownloaded}`); // Debug log
    // --- End Define isDownloaded ---

    let allImages = ['image/placeholder.svg'];
    if (item.image_urls) {
        if (Array.isArray(item.image_urls)) {
            allImages = item.image_urls.filter(url => typeof url === 'string' && url.startsWith('http'));
        } else if (typeof item.image_urls === 'string') {
            try {
                const parsedImages = JSON.parse(item.image_urls);
                if (Array.isArray(parsedImages)) {
                    allImages = parsedImages.filter(url => typeof url === 'string' && url.startsWith('http'));
                } else if (item.image_urls.startsWith('http')) {
                    allImages = [item.image_urls];
                }
            } catch (e) {
                if (item.image_urls.startsWith('http')) {
                    allImages = [item.image_urls];
                }
            }
        }
    }
    if (!Array.isArray(allImages) || allImages.length === 0) {
        allImages = ['image/placeholder.svg'];
    }

    const thumbnailsHtml = allImages.length > 1 ? allImages
        .map((image, index) => `
            <img
                src="${image}"
                alt="Thumbnail ${index + 1}"
                class="thumbnail ${index === mainImageIndex ? 'selected' : ''}"
                style="flex-shrink: 0; width: 80px; height: 80px; object-fit: contain; border: 2px solid ${index === mainImageIndex ? 'darkorange' : 'orange'}; border-radius: 5px; cursor: pointer; transition: transform 0.3s ease, border-color 0.3s ease;"
                onclick="updateMainImage('${image}', ${index})"
            >
        `).join('') : '';

    modalContent.innerHTML = `
            <div style="position: relative; margin-bottom: 1rem;">
                <div class="main-image-container" style="position: relative; text-align: center;">
                     <img id="mainImage" src="${allImages[mainImageIndex]}" alt="${item.name || 'Mod Image'}" class="main-image">
                     <button class="modal-image-close-btn" onclick="closeModal()" title="Close">&times;</button>
                     <!-- أيقونة ااستفهام -->
                     <button id="helpIcon" class="help-icon" onclick="showNewInstallInstructions()" title="كيفية اتحمي واتركيب">
                         <i class="fa-solid fa-question"></i>
                     </button>
                </div>
                ${allImages.length > 1 ? `
                <div class="thumbnail-container" style="display: flex; justify-content: center; gap: 10px; margin-top: 10px; overflow-x: auto; padding: 10px 0;">
                    ${thumbnailsHtml}
                </div>` : ''}
            </div>
            <h2 style="font-size: 1.75rem; margin-bottom: 0.5rem; text-align: center; color: #f1f5f9;">${item.name || t('mod_name')}</h2>
            <center><div id="text-container" style="margin-bottom: 1rem; padding: 0 15px; color: #cbd5e1;">
                <span class="initial-textD">${getLocalizedDescription(item)}</span>
            </div></center>
            <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 1rem; margin-bottom: 20px; text-align: center;">
                <div class="tag">${t('size')}: ${item.size || 'N/A'}</div>
                <div class="tag">${t('version')}: ${item.version || 'N/A'}</div>
                <div class="tag">${t('category')}: ${item.category || 'N/A'}</div>
            </div>

            <!-- قسم معومات صانع امود -->
            <div id="creatorInfoSection" style="margin-bottom: 20px; padding: 0 15px;">
                <div style="
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 12px;
                    padding: 15px;
                    border: 2px solid #ffcc00;
                    margin-bottom: 15px;
                ">
                    <h3 style="color: #ffcc00; margin-bottom: 10px; font-size: 1.1rem; text-align: center;">
                        ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'معومات صانع امود' : 'Mod Creator Info'}
                    </h3>
                    <div id="creatorInfoContent" style="color: #ffffff; text-align: center;">
                        <div style="color: #ccc; font-size: 0.9rem;">
                            ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'جاري تحمي معومات اصانع...' : 'Loading creator info...'}
                        </div>
                    </div>
                </div>
            </div>
        <div style="position: fixed; bottom: 0; left: 0; right: 0; background: linear-gradient(to right, #FFCC00, #FFA500); padding: 8px 8px 23px 8px; display: flex; justify-content: center; align-items: center; gap: 6px; box-shadow: 0 -2px 5px rgba(0,0,0,0.2); height: 85px; z-index: 100;">
            <!-- Download Button / Progress Area -->
            <button
                class="download-btn ${isDownloaded ? 'downloaded' : ''}"
                data-mod-id="${item.id}"
                onclick='${!item.download_url || !item.download_url.startsWith('http') ? `showDownloadErrorMessage(${JSON.stringify(item.name || 'Unnamed Mod')}, ${JSON.stringify(t('download_link_unavailable'))})` : `handleDownload(${JSON.stringify(item.id)}, ${JSON.stringify(item.name || 'Unnamed Mod')}, ${JSON.stringify(item.download_url || '')})`}'
                ${!item.download_url || !item.download_url.startsWith('http') ? 'style="background-color: #6c757d; cursor: not-allowed;"' : ''}
                title="${isDownloaded ? 'Open Mod' : 'Download Mod'}"
                style="background-color: #ffc107; color: #fff; border: none; padding: 0 12px; border-radius: 8px; font-weight: bold; font-size: 16px; display: flex; align-items: center; justify-content: center; gap: 5px; cursor: pointer; height: 57px; flex-grow: 1; position: relative; overflow: hidden; box-shadow: 0 2px 3px rgba(0,0,0,0.2);"
            >
                <i class="fa-solid ${isDownloaded ? 'fa-folder-open' : 'fa-download'}" style="color: #fff; margin-right: 5px; font-size: 18px;"></i>
                <span class="download-btn-text">${isDownloaded ? 'Open' : (!item.download_url || !item.download_url.startsWith('http') ? t('link_unavailable') : 'Download')}</span>
                <!-- Progress Indicator (Initially Hidden) -->
                <div class="download-progress-container" data-mod-id="${item.id}" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center; border-radius: 8px;">
                    <div class="download-progress-bar" data-mod-id="${item.id}" style="position: absolute; top: 0; left: 0; height: 100%; width: 0%; border-radius: 8px; transition: width 0.2s ease-out;"></div>
                    <span class="download-progress-text" data-mod-id="${item.id}" style="position: relative; z-index: 1; color: white; font-size: 14px; font-weight: bold;">0%</span>
                </div>
            </button>
            <!-- End Download Button / Progress Area -->
            <button title="Total Downloads" style="background-color: #ffd700; color: #f1f5f9; border: none; padding: 5px; border-radius: 8px; font-weight: bold; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 3px; cursor: default; width: 55px; height: 57px; box-shadow: 0 2px 3px rgba(0,0,0,0.15);">
                <i class="fa-solid fa-arrow-down" style="font-size: 18px; color: #f1f5f9;"></i>
                <span class="download-count" data-mod-id="${item.id}" style="font-size: 11px; color: #f1f5f9;">${formatCount(item.downloads || 0)}</span>
            </button>
            <button class="like-button" onclick="toggleLike('${item.id}', '${item.name || 'Unnamed Mod'}', this)" title="Like Mod" style="background-color: #ffd700; color: #f1f5f9; border: none; padding: 5px; border-radius: 8px; font-weight: bold; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 3px; cursor: pointer; width: 55px; height: 57px; box-shadow: 0 2px 3px rgba(0,0,0,0.15);">
                <i class="fa-solid fa-heart heart-icon" style="font-size: 18px; color: #f1f5f9;"></i>
                <span class="like-count" data-mod-id="${item.id}" style="font-size: 11px; color: #f1f5f9;">${formatCount(item.likes || 0)}</span>
            </button>
            <button onclick="showModCreatorInfo('${item.id}')" title="Mod Creator Info" style="background-color: #ffd700; color: #f1f5f9; border: none; padding: 5px; border-radius: 8px; font-weight: bold; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 3px; cursor: pointer; width: 55px; height: 57px; box-shadow: 0 2px 3px rgba(0,0,0,0.15);">
                <i class="fa-solid fa-copyright" style="font-size: 18px; color: #f1f5f9;"></i>
                <span style="font-size: 11px; color: #f1f5f9;">Creator</span>
            </button>
        </div>
    `;

    modal.style.display = "block";
    document.documentElement.classList.add('modal-open'); // Add class to HTML
    document.body.classList.add('modal-open');

    // Hide bottom bar when modal opens
    const bottomBar = document.querySelector('.bottom-fixed-bar');
    if (bottomBar) bottomBar.classList.add('hidden');

    // إضافة حالة للتاريخ لمعالجة زر الرجوع بشكل صحيح
    pushModalState();

    // Incrementing clicks is now handled once at the beginning of the function.

    // --- إضافة تأثيرات أيقونة ااستفهام ---
    setTimeout(() => {
        initializeHelpIcon();
    }, 100); // تأخير قصير تأكد من تحمي اعناصر

    // تحمي معومات صانع امود في اقسم اجديد
    setTimeout(() => {
        loadCreatorInfoInSection(item.id);
    }, 200);
}

// متغير منع انقر امتعدد
let isCreatorInfoLoading = false;

// داة تحمي معومات صانع امود في اقسم
async function loadCreatorInfoInSection(modId) {
    try {
        const contentElement = document.getElementById('creatorInfoContent');
        if (!contentElement) return;

        // جب بيانات امود من قاعدة ابيانات
        const { data: mod, error } = await supabaseClient
            .from('mods')
            .select('creator_name, creator_contact_info, creator_social_channels, custom_social_site_name, custom_social_site_url')
            .eq('id', modId)
            .single();

        if (error) {
            console.error('خطأ في جب بيانات صانع امود:', error);
            contentElement.innerHTML = `
                <div style="color: #ff6b6b; font-size: 0.9rem;">
                    ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'خطأ في تحمي معومات اصانع' : 'Error loading creator info'}
                </div>
            `;
            return;
        }

        const isArabic = (localStorage.getItem('selectedLanguage') || 'en') === 'ar';

        // إنشاء محتوى معومات اصانع
        let creatorContent = '';

        // اسم اصانع
        if (mod.creator_name) {
            creatorContent += `
                <div style="margin-bottom: 10px;">
                    <span style="color: #ffcc00; font-weight: bold;">
                        ${isArabic ? 'اصانع:' : 'Creator:'}
                    </span>
                    <span style="color: #ffffff; margin-left: 8px;">
                        ${mod.creator_name}
                    </span>
                </div>
            `;
        }

        // وسائ اتواص ااجتماعي
        let socialChannelsHtml = '';

        // استخدام نظام اأيقونات اجديد
        if (window.socialIconsManager) {
            socialChannelsHtml = window.socialIconsManager.processSocialChannels(
                mod.creator_social_channels,
                mod.custom_social_site_url,
                mod.custom_social_site_name,
                'small'
            );
        } else {
            // Fallback نظام اقديم
            if (mod.creator_social_channels) {
                try {
                    const socialChannels = typeof mod.creator_social_channels === 'string'
                        ? JSON.parse(mod.creator_social_channels)
                        : mod.creator_social_channels;

                    const socialIcons = {
                        youtube: { icon: 'fab fa-youtube', color: '#FF0000' },
                        twitter: { icon: 'fab fa-twitter', color: '#1DA1F2' },
                        instagram: { icon: 'fab fa-instagram', color: '#E4405F' },
                        facebook: { icon: 'fab fa-facebook', color: '#1877F2' },
                        discord: { icon: 'fab fa-discord', color: '#5865F2' },
                        tiktok: { icon: 'fab fa-tiktok', color: '#000000' },
                        telegram: { icon: 'fab fa-telegram', color: '#0088CC' },
                        github: { icon: 'fab fa-github', color: '#333333' }
                    };

                    socialChannelsHtml = Object.entries(socialChannels)
                        .filter(([platform, url]) => url && url.trim())
                        .map(([platform, url]) => {
                            const iconData = socialIcons[platform.toLowerCase()] || { icon: 'fas fa-link', color: '#ffcc00' };
                            return `
                                <a href="${url}" target="_blank" style="
                                    display: inline-flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 30px;
                                    height: 30px;
                                    background: ${iconData.color};
                                    border-radius: 50%;
                                    margin: 3px;
                                    text-decoration: none;
                                    transition: transform 0.2s ease;
                                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                                " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                    <i class="${iconData.icon}" style="color: white; font-size: 14px;"></i>
                                </a>
                            `;
                        }).join('');
                } catch (e) {
                    console.error('خطأ في تحي قنوات اتواص ااجتماعي:', e);
                }
            }

            // إضافة اموقع امخصص نظام اقديم
            if (mod.custom_social_site_url && mod.custom_social_site_name && !window.socialIconsManager) {
                socialChannelsHtml += `
                    <a href="${mod.custom_social_site_url}" target="_blank" style="
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        width: 30px;
                        height: 30px;
                        background: #ffcc00;
                        border-radius: 50%;
                        margin: 3px;
                        text-decoration: none;
                        transition: transform 0.2s ease;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'" title="${mod.custom_social_site_name}">
                        <i class="fas fa-globe" style="color: #000; font-size: 14px;"></i>
                    </a>
                `;
            }
        }



        if (socialChannelsHtml) {
            creatorContent += `
                <div style="margin-bottom: 10px;">
                    <div style="color: #ffcc00; font-weight: bold; margin-bottom: 8px;">
                        ${isArabic ? 'وسائ اتواص:' : 'Social Media:'}
                    </div>
                    <div style="text-align: center;">
                        ${socialChannelsHtml}
                    </div>
                </div>
            `;
        }

        // إذا م توجد معومات
        if (!creatorContent) {
            creatorContent = `
                <div style="color: #ccc; font-size: 0.9rem;">
                    ${isArabic ? 'ا توجد معومات متاحة عن اصانع' : 'No creator information available'}
                </div>
            `;
        }

        contentElement.innerHTML = creatorContent;

    } catch (error) {
        console.error('خطأ في تحمي معومات صانع امود:', error);
        const contentElement = document.getElementById('creatorInfoContent');
        if (contentElement) {
            contentElement.innerHTML = `
                <div style="color: #ff6b6b; font-size: 0.9rem;">
                    ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'خطأ في تحمي معومات اصانع' : 'Error loading creator info'}
                </div>
            `;
        }
    }
}

// داة عرض معومات صانع امود
async function showModCreatorInfo(modId) {
    // منع انقر امتعدد
    if (isCreatorInfoLoading) {
        console.log('⚠️ Creator info is already loading, ignoring click');
        return;
    }

    // اتحقق من وجود مربع مفتوح مسبقاً
    const existingOverlay = document.getElementById('creator-info-overlay');
    if (existingOverlay) {
        console.log('⚠️ Creator info dialog already open, ignoring click');
        return;
    }

    // تعيين حاة اتحمي
    isCreatorInfoLoading = true;
    console.log('🔄 Loading creator info for mod:', modId);

    try {
        // عرض مؤشر تحمي سريع
        showQuickLoadingIndicator();

        // جب بيانات امود من قاعدة ابيانات
        const { data: mod, error } = await supabaseClient
            .from('mods')
            .select('creator_name, creator_contact_info, creator_social_channels, custom_social_site_name, custom_social_site_url, name')
            .eq('id', modId)
            .single();

        if (error) {
            console.error('خطأ في جب بيانات صانع امود:', error);
            return;
        }

        // اتحقق من وجود حقوق طبع مخصصة هذا امود
        const { data: customCopyright, error: copyrightError } = await supabaseClient
            .from('custom_copyright_mods')
            .select('is_active')
            .eq('mod_id', modId)
            .eq('is_active', true)
            .single();

        const hasCustomCopyright = !copyrightError && customCopyright;

        // احصو عى غة امستخدم
        const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
        const isArabic = userLanguage === 'ar';

        // إنشاء امربع
        const overlay = document.createElement('div');
        overlay.id = 'creator-info-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100000;
            animation: fadeIn 0.3s ease;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #000000;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 3px solid #ffcc00;
            box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
            animation: slideInUp 0.4s ease;
            position: relative;
            direction: ${isArabic ? 'rtl' : 'ltr'};
        `;

        // إنشاء امحتوى - استخدام نظام اأيقونات اجديد
        let socialChannelsHtml = '';

        if (window.socialIconsManager) {
            socialChannelsHtml = window.socialIconsManager.processSocialChannels(
                mod.creator_social_channels,
                mod.custom_social_site_url,
                mod.custom_social_site_name,
                'medium'
            );
        } else {
            // Fallback نظام اقديم
            if (mod.creator_social_channels) {
                try {
                    const socialChannels = typeof mod.creator_social_channels === 'string'
                        ? JSON.parse(mod.creator_social_channels)
                        : mod.creator_social_channels;

                    const socialIcons = {
                        youtube: { icon: 'fab fa-youtube', color: '#FF0000' },
                        twitter: { icon: 'fab fa-twitter', color: '#1DA1F2' },
                        instagram: { icon: 'fab fa-instagram', color: '#E4405F' },
                        facebook: { icon: 'fab fa-facebook', color: '#1877F2' },
                        discord: { icon: 'fab fa-discord', color: '#5865F2' },
                        tiktok: { icon: 'fab fa-tiktok', color: '#000000' },
                        telegram: { icon: 'fab fa-telegram', color: '#0088CC' },
                        github: { icon: 'fab fa-github', color: '#333333' }
                    };

                    socialChannelsHtml = Object.entries(socialChannels)
                        .filter(([platform, url]) => url && url.trim())
                        .map(([platform, url]) => {
                            const iconData = socialIcons[platform.toLowerCase()] || { icon: 'fas fa-link', color: '#ffcc00' };
                            return `
                                <a href="${url}" target="_blank" style="
                                    display: inline-flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 45px;
                                    height: 45px;
                                    background: ${iconData.color};
                                    border-radius: 50%;
                                    margin: 5px;
                                    text-decoration: none;
                                    transition: transform 0.2s ease;
                                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                                " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                    <i class="${iconData.icon}" style="color: white; font-size: 20px;"></i>
                                </a>
                            `;
                        }).join('');
                } catch (e) {
                    console.error('خطأ في تحي قنوات اتواص ااجتماعي:', e);
                }
            }

            // إضافة اموقع امخصص نظام اقديم
            if (mod.custom_social_site_url && mod.custom_social_site_name && !window.socialIconsManager) {
                socialChannelsHtml += `
                    <a href="${mod.custom_social_site_url}" target="_blank" style="
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        width: 45px;
                        height: 45px;
                        background: #ffcc00;
                        border-radius: 50%;
                        margin: 5px;
                        text-decoration: none;
                        transition: transform 0.2s ease;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                    " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'" title="${mod.custom_social_site_name}">
                        <i class="fas fa-globe" style="color: #000; font-size: 20px;"></i>
                    </a>
                `;
            }
        }



        const copyrightText = {
            title: t('creator_info_title'),
            creatorLabel: t('creator_label'),
            socialLabel: t('social_label'),
            copyrightTitle: t('copyright_title'),
            copyrightDesc: hasCustomCopyright ? t('custom_copyright_desc') : t('copyright_desc'),
            contactTitle: t('contact_title'),
            contactInfo: t('contact_info'),
            noCreator: t('no_creator'),
            noSocial: t('no_social'),
            closeBtn: t('close_btn')
        };

        modal.innerHTML = `
            <!-- زر اإغاق -->
            <button onclick="closeCreatorInfo()" style="
                position: absolute;
                top: 15px;
                ${isArabic ? 'left' : 'right'}: 15px;
                background: transparent;
                border: none;
                color: #ffcc00;
                font-size: 1.5rem;
                cursor: pointer;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            " onmouseover="this.style.backgroundColor='rgba(255,204,0,0.2)'" onmouseout="this.style.backgroundColor='transparent'">&times;</button>

            <!-- اعنوان -->
            <h2 style="
                color: #ffcc00;
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
                font-family: 'Press Start 2P', 'Minecraft-Fallback', 'Courier New', monospace;
                text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
            ">
                <i class="fa-solid fa-copyright" style="margin-${isArabic ? 'left' : 'right'}: 10px;"></i>
                ${copyrightText.title}
            </h2>

            <!-- اسم صانع امود -->
            <div style="margin-bottom: 20px;">
                <h3 style="color: #ffcc00; margin-bottom: 10px; font-size: 1.1rem;">
                    ${copyrightText.creatorLabel}
                </h3>
                <p style="
                    color: white;
                    background: rgba(255, 204, 0, 0.1);
                    padding: 10px;
                    border-radius: 8px;
                    border: 1px solid #ffcc00;
                    font-size: 1rem;
                    font-weight: bold;
                ">
                    ${mod.creator_name || copyrightText.noCreator}
                </p>
            </div>

            <!-- وسائ اتواص ااجتماعي -->
            <div style="margin-bottom: 25px;">
                <h3 style="color: #ffcc00; margin-bottom: 15px; font-size: 1.1rem;">
                    ${copyrightText.socialLabel}
                </h3>
                <div style="
                    text-align: center;
                    background: rgba(255, 204, 0, 0.1);
                    padding: 15px;
                    border-radius: 8px;
                    border: 1px solid #ffcc00;
                    min-height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-wrap: wrap;
                ">
                    ${socialChannelsHtml || `<p style="color: #888; margin: 0;">${copyrightText.noSocial}</p>`}
                </div>
            </div>

            <!-- معومات حقوق اطبع وانشر -->
            <div style="
                background: ${hasCustomCopyright ? 'rgba(255, 107, 107, 0.1)' : 'rgba(255, 204, 0, 0.1)'};
                border: 2px solid ${hasCustomCopyright ? '#ff6b6b' : '#ffcc00'};
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            ">
                <h3 style="
                    color: ${hasCustomCopyright ? '#ff6b6b' : '#ffcc00'};
                    margin-bottom: 15px;
                    font-size: 1.1rem;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                ">
                    <i class="${hasCustomCopyright ? 'fa-solid fa-exclamation-triangle' : 'fa-solid fa-shield-alt'}"></i>
                    ${copyrightText.copyrightTitle}
                    ${hasCustomCopyright ? '<span style="background: #ff6b6b; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; margin-left: 8px;">خاص</span>' : ''}
                </h3>
                <p style="
                    color: white;
                    line-height: 1.6;
                    margin-bottom: 15px;
                    font-size: 0.95rem;
                ">
                    ${copyrightText.copyrightDesc}
                </p>

                <div style="
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 8px;
                    padding: 15px;
                    border-left: 4px solid #ffcc00;
                ">
                    <h4 style="color: #ffcc00; margin-bottom: 10px; font-size: 1rem;">
                        ${copyrightText.contactTitle}
                    </h4>
                    <p style="color: #ffffff; margin: 0 0 10px 0; font-size: 0.9rem;">
                        ${copyrightText.contactInfo}
                    </p>
                    <div style="
                        background: rgba(255, 204, 0, 0.1);
                        border: 1px solid #ffcc00;
                        border-radius: 6px;
                        padding: 10px;
                        margin-top: 10px;
                    ">
                        <p style="color: #ffcc00; margin: 0; font-size: 0.9rem; font-weight: bold;">
                            📧 تواص / Contact:
                            <a href="mailto:<EMAIL>" style="color: #ffffff; text-decoration: none;">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- زر اإغاق -->
            <div style="text-align: center;">
                <button onclick="closeCreatorInfo()" style="
                    background: linear-gradient(45deg, #ffcc00, #ff9800);
                    color: #ffffff;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 8px;
                    font-size: 1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s;
                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(255,204,0,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    ${copyrightText.closeBtn}
                </button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // إخفاء مؤشر اتحمي بعد نجاح اعمية
        hideQuickLoadingIndicator();

        // منع اتمرير في اخفية
        document.body.style.overflow = 'hidden';

        console.log('✅ Creator info dialog displayed successfully');

        // إغاق عند انقر خارج امربع
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                closeCreatorInfo();
            }
        });

    } catch (error) {
        console.error('❌ خطأ في عرض معومات صانع امود:', error);

        // إزاة مؤشر اتحمي في حاة اخطأ
        hideQuickLoadingIndicator();

        // عرض رساة خطأ مستخدم
        showErrorMessage('حدث خطأ في تحمي معومات صانع امود');

    } finally {
        // إعادة تعيين حاة اتحمي
        isCreatorInfoLoading = false;
        console.log('✅ Creator info loading completed');
    }
}

// داة إغاق مربع معومات صانع امود
function closeCreatorInfo() {
    const overlay = document.getElementById('creator-info-overlay');
    if (overlay) {
        overlay.remove();
        document.body.style.overflow = '';
    }

    // إزاة مؤشر اتحمي إذا كان موجود
    hideQuickLoadingIndicator();

    // إعادة تعيين حاة اتحمي
    isCreatorInfoLoading = false;
    console.log('🔒 Creator info dialog closed');
}

// داة عرض مؤشر تحمي سريع
function showQuickLoadingIndicator() {
    // إزاة أي مؤشر تحمي موجود
    hideQuickLoadingIndicator();

    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'quick-loading-indicator';
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 99999;
        animation: fadeIn 0.2s ease;
    `;

    const spinner = document.createElement('div');
    spinner.style.cssText = `
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 204, 0, 0.3);
        border-top: 4px solid #ffcc00;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    `;

    loadingOverlay.appendChild(spinner);
    document.body.appendChild(loadingOverlay);

    console.log('⏳ Quick loading indicator shown');
}

// داة إخفاء مؤشر اتحمي اسريع
function hideQuickLoadingIndicator() {
    const loadingOverlay = document.getElementById('quick-loading-indicator');
    if (loadingOverlay) {
        loadingOverlay.remove();
        console.log('✅ Quick loading indicator hidden');
    }
}

// داة عرض رساة خطأ
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #ff4444;
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        z-index: 100001;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        animation: slideInUp 0.3s ease;
    `;
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    // إزاة ارساة بعد 3 ثوان
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 3000);
}

// داة تهيئة أيقونة ااستفهام مع اتأثيرات
function initializeHelpIcon() {
    const helpIcon = document.getElementById('helpIcon');
    if (!helpIcon) return;

    // إظهار اأيقونة واضحة في ابداية
    helpIcon.style.opacity = '1';
    helpIcon.style.transform = 'scale(1)';

    // بعد 3 ثوان، جع اأيقونة شفافة
    setTimeout(() => {
        helpIcon.style.opacity = '0.3';
    }, 3000);

    // إضافة مستمع انقر
    helpIcon.addEventListener('click', function() {
        // جع اأيقونة واضحة مؤقتاً عند انقر
        this.style.opacity = '1';
        this.style.transform = 'scale(1.1)';

        // إضافة تأثير نبضة
        this.classList.add('help-icon-pulse');

        // إزاة اتأثير بعد فترة قصيرة
        setTimeout(() => {
            this.style.transform = 'scale(1)';
            this.classList.remove('help-icon-pulse');

            // اعودة شفافية بعد ثانية واحدة
            setTimeout(() => {
                this.style.opacity = '0.3';
            }, 1000);
        }, 200);
    });

    // إضافة تأثير hover
    helpIcon.addEventListener('mouseenter', function() {
        this.style.opacity = '1';
        this.style.transform = 'scale(1.05)';
    });

    helpIcon.addEventListener('mouseleave', function() {
        this.style.opacity = '0.3';
        this.style.transform = 'scale(1)';
    });
}

window.updateMainImage = (src, index) => {
    const mainImage = document.getElementById('mainImage');
    if (mainImage) {
        mainImage.src = src;
        // التأكد من أن الصورة تحتفظ بالـ class الصحيح
        mainImage.className = 'main-image';
    }
    const thumbnails = document.querySelectorAll('#modalContent .thumbnail');
    thumbnails.forEach((img, i) => {
        img.classList.toggle('selected', i === index);
        img.style.borderColor = i === index ? 'darkorange' : 'orange';
    });
};

// --- Infinite Scroll / Load More (Currently not used, keep if needed later) ---
function addLoadingIndicator(container, sectionModsContext) { /* ... */ }
function loadMoreMods(container, loadingIndicator, sectionModsContext) { /* ... */ }


// --- Filtering Logic (Updated: Switch Display Function) ---
function filterItems(category) {
    // console.log(`*** filterItems ENTRY POINT for category: ${category} ***`); // Removed debug log
    console.log(`>>> filterItems called for category: ${category}`); // Existing log
    console.log(`Filtering by category: ${category}`);
    currentCategory = category; // Update global state

    // Set default sort criteria based on category
    if (['Addons', 'Texture', 'Shaders', 'Maps', 'Seeds', 'Skin Pack'].includes(category)) {
        currentSortBy = 'likes';
        currentSortAscending = false; // Most liked first
    } else if (category === 'News') {
        currentSortBy = 'created_at';
        currentSortAscending = false; // Newest first for News
    } else if (category === 'Suggested') {
        currentSortBy = 'created_at';
        currentSortAscending = false; // Newest first for Suggested
    } else {
        // Default for 'All' (though displayModsBySection handles its own fetching)
        // or any other future categories.
        currentSortBy = 'created_at';
        currentSortAscending = false; // Newest first
    }
    updateActiveSortButton(); // Update UI for sort buttons

    // Decide which display function to call and manage sort button visibility
    const sortButtons = document.getElementById('sortButtons');
    if (category === 'All') {
        displayModsBySection();
        if (sortButtons) {
            sortButtons.style.display = 'none'; // Hide sort buttons for the segmented 'All' view
        }
    } else {
        // For single category view (Addons, Texture, Shaders, etc.)
        // displaySingleCategory will use the currentSortBy and currentSortAscending set above
        displaySingleCategory(category, currentSortBy, currentSortAscending);
        if (sortButtons) {
            sortButtons.style.display = 'flex'; // Show sort buttons for single category view
            sortButtons.classList.remove('initial-hidden-animate'); // Ensure animation reset
            sortButtons.classList.add('animate-visible'); // Trigger animation if needed
        }
    }

    // Update active category button in the top bar
    document.querySelectorAll('#categories .category-btn').forEach(btn => {
        const btnCategory = btn.getAttribute('data-category');
        btn.classList.toggle('active-category', btnCategory === category);
    });
}

// --- Sorting Logic (Re-added) ---
function sortMods(criteria) {
     console.log(`>>> sortMods called for criteria: ${criteria}`); // Added entry log
     console.log(`Sorting by: ${criteria}`);
     let sortBy = 'created_at';
     let ascending = false;

     switch(criteria) {
        case 'likes':
            sortBy = 'likes';
            ascending = false; // Most first
            break;
        case 'downloads':
            sortBy = 'downloads';
            ascending = false; // Most first
            break;
        case 'newest':
            sortBy = 'created_at';
            ascending = false; // Newest first (descending)
            break;
        case 'oldest':
            sortBy = 'created_at';
            ascending = true; // Oldest first (ascending)
            break;
        default:
            sortBy = 'created_at';
            ascending = false;
     }

     // Update global sort state
     currentSortBy = sortBy;
     currentSortAscending = ascending;

     // Re-display the current single category with the new sort order
     // When a sort button is clicked, we want to display all mods sorted
     // So, set currentCategory to 'All' and then display the single category view.
     currentCategory = 'All';
     displaySingleCategory(currentCategory, currentSortBy, currentSortAscending);

     // Highlight active sort button
     updateActiveSortButton(criteria);
}

// Helper to update active sort button visuals
function updateActiveSortButton(activeCriteria = null) {
    document.querySelectorAll('.sort-btn').forEach(btn => {
        const onclickAttr = btn.getAttribute('onclick');
        const match = onclickAttr ? onclickAttr.match(/sortMods\('([^']+)'\)/) : null;
        const btnCriteria = match ? match[1] : null;

        // Determine the criteria represented by the current global state
        let currentCriteria = null;
        if (currentSortBy === 'created_at') {
            currentCriteria = currentSortAscending ? 'oldest' : 'newest';
        } else if (currentSortBy === 'likes') {
            currentCriteria = 'likes';
        } else if (currentSortBy === 'downloads') {
            currentCriteria = 'downloads';
        }

        // Activate the button corresponding to the current sort state
        btn.classList.toggle('active-sort', btnCriteria === currentCriteria);
    });
}


// --- Create Banner Ads Table if it doesn't exist ---
async function createBannerAdsTableIfNeeded() {
    try {
        // Check if the table exists by trying to select from it
        const { error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('id')
            .limit(1);

        // If there's a permission error or the table doesn't exist
        if (error && (error.message.includes('permission denied') || error.message.includes('does not exist'))) {
            console.log('Banner ads table might not exist.');
            console.warn('Please create the banner_ads table manually in Supabase SQL Editor.');
            console.info('Execute the SQL from database/missing_tables.sql to create all required tables.');

            // Try to create the table using RPC if available
            try {
                const { error: createError } = await supabaseClient.rpc('execute_sql', {
                    sql_query: `
                        CREATE TABLE IF NOT EXISTS ${BANNER_ADS_TABLE} (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            description TEXT,
                            image_url TEXT NOT NULL,
                            target_url TEXT,
                            display_order INTEGER DEFAULT 0,
                            is_active BOOLEAN DEFAULT true,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                        );
                    `
                });

                if (createError) {
                    console.warn('Could not create banner ads table via RPC:', createError.message);
                    console.warn('Please execute database/missing_tables.sql in Supabase SQL Editor');
                } else {
                    console.log('Banner ads table created successfully');
                }
            } catch (rpcError) {
                console.warn('RPC function execute_sql not available. Please create tables manually.');
                console.info('Execute database/missing_tables.sql in Supabase SQL Editor');
            }

            // Also check the featured_addons table
            try {
                const { error: featuredError } = await supabaseClient
                    .from(FEATURED_ADDONS_TABLE)
                    .select('id')
                    .limit(1);

                if (featuredError && (featuredError.message.includes('permission denied') || featuredError.message.includes('does not exist'))) {
                    console.log('Featured addons table might not exist.');

                    try {
                        const { error: createFeaturedError } = await supabaseClient.rpc('execute_sql', {
                            sql_query: `
                                CREATE TABLE IF NOT EXISTS ${FEATURED_ADDONS_TABLE} (
                                    id SERIAL PRIMARY KEY,
                                    mod_id UUID NOT NULL REFERENCES mods(id),
                                    is_active BOOLEAN DEFAULT true,
                                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                                );
                            `
                        });

                        if (createFeaturedError) {
                            console.warn('Could not create featured addons table via RPC:', createFeaturedError.message);
                        } else {
                            console.log('Featured addons table created successfully');
                        }
                    } catch (rpcError) {
                        console.warn('RPC function execute_sql not available for featured addons table.');
                    }
                }
            } catch (err) {
                console.warn('Error checking featured addons table:', err.message);
            }
        } else {
            console.log('Banner ads table already exists');
        }
    } catch (error) {
        console.error('Unexpected error checking/creating banner ads table:', error);
    }
}

// --- Initial Load & Event Listeners ---
document.addEventListener("DOMContentLoaded", async function () {
    console.log(">>> DOMContentLoaded event fired."); // Added log

    // Initialize Supabase if not already done
    if (!supabaseClient) {
        await initializeSupabase();
    }
    const categoriesContainer = document.getElementById("categories");
    const sortButtons = document.querySelectorAll("#sortButtons .sort-btn");
    const mainContentContainers = [
        document.getElementById('addons-section'),
        document.getElementById('texture-pack-section'),
        document.getElementById('shaders-section'),
        document.getElementById('singleCategoryContainer')
    ].filter(Boolean); // Filter out nulls if some containers don't exist

    // --- Event Delegation for Category Buttons (Moved to separate function) ---
    // Removed listener attachment from here
    // --- End Event Delegation ---


    // REMOVED Event Delegation for Mod Item Clicks


    // Add click listeners to SORT buttons (keep individual listeners attached on DOMContentLoaded)
    sortButtons.forEach(button => {
        button.addEventListener("click", function(event) {
            const onclickAttr = this.getAttribute('onclick');
            const match = onclickAttr ? onclickAttr.match(/sortMods\('([^']+)'\)/) : null;
            if (match && match[1]) {
                sortMods(match[1]); // Call sortMods with the criteria
            }
        });
    });


    const searchButton = document.querySelector(".search-btn");
    if (searchButton) {
        searchButton.addEventListener("click", () => {
            if (window.location.pathname.endsWith('search.html')) {
                 console.log("Already on search page.");
                 return;
            }
            window.location.href = 'search.html';
        });
    } else {
        console.warn("Search button not found.");
    }

    // --- Initial Page Load ---
    // Apply the animation class to the main content wrapper
    const mainContentWrapper = document.getElementById('main-content-wrapper');
    if (mainContentWrapper) {
        mainContentWrapper.classList.add('animate-visible');
    }

    // Set 'All' active initially using data-category
    const allButton = document.querySelector("#categories button[data-category='All']");
    if (allButton) {
        allButton.classList.add('active-category');
    } else {
        console.warn("'All' category button not found for initial activation.");
    }

    // Initial display is "All" category (sectioned view)
    // Defer the initial heavy load slightly to allow UI/listeners to become responsive first
    setTimeout(async () => {
        console.log(">>> Starting initial data load (displayModsBySection) (after 50ms delay)..."); // Added log

        try {
            // Check network connectivity before starting
            const isConnected = await checkNetworkConnectivity();
            if (!isConnected) {
                console.error('No network connectivity detected during initial load');
                showNetworkError();
                return;
            }

            // تهيئة نظام اشعبية اديناميكي
            await initializePopularitySystem();

            // --- Re-enabled initial load ---
            displayModsBySection();
            // --- End Re-enabled ---
            updateActiveSortButton(); // Set initial sort button state (though hidden)
            console.log(">>> Initial data load function called."); // Re-enabled log

            // REMOVED: Call to simulateInitialClick
            // setTimeout(simulateInitialClick, 200);
        } catch (error) {
            console.error('Error during initial data load:', error);
            if (error.message && (error.message.includes('Failed to fetch') ||
                                  error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                                  error.message.includes('TypeError: Failed to fetch'))) {
                showNetworkError();
            }
        }

    }, 50); // Keep slight delay for initial data load


    // --- Button Press Animation (Listeners removed to potentially fix click delay issue) ---
    // document.querySelectorAll(".download-btn, .like-button, .category-btn, .sort-btn, .close-btn, .back-btn").forEach(button => {
    //     const removePressedClass = () => button.classList.remove('button-pressed');
    //     // Touch listeners removed
    // });

    // Initialize lazy loading for initially loaded images if any (though display functions handle it now)
    // initializeLazyLoading('.lazy-load');

    // Fetch and display update notification
    fetchAndDisplayUpdateNotification();
    fetchAndDisplayAppAnnouncements(); // أضف هذا ااستدعاء
    loadDrawerLinks(); // أضف هذا ااستدعاء
    createBannerAdsTableIfNeeded(); // Create banner ads table and featured addons table if they don't exist

    // Fetch and display entry subscription ad after other initial loads
    fetchAndDisplayEntrySubscriptionAd();

    // Initialize floating subscription icon
    initializeFloatingSubscriptionIcon();


    // Reverted: Removed simulated touch/click events as they didn't solve the issue.
});

async function loadDrawerLinks() {
    const drawerContainer = document.querySelector(".drawer");

    if (!drawerContainer) {
        console.error("Drawer container element (.drawer) not found.");
        return;
    }

    // Add user settings link
    const userSettingsLinkContainer = document.createElement('div');
    userSettingsLinkContainer.id = 'user-settings-link-container';

    const userSettingsLink = document.createElement('a');
    userSettingsLink.href = 'user-settings.html';
    userSettingsLink.innerHTML = `<i class="fas fa-user-cog"></i> ${t('settings')}`;
    userSettingsLink.style.cssText = `
        display: block; padding: 12px 15px; text-decoration: none;
        color: #ffffff; /* White color for user settings link */
        border-bottom: 1px solid #444;
        transition: background-color 0.2s;
        margin-bottom: 15px;
        font-weight: bold;
    `;
    userSettingsLink.onmouseover = () => userSettingsLink.style.backgroundColor = '#444';
    userSettingsLink.onmouseout = () => userSettingsLink.style.backgroundColor = 'transparent';

    userSettingsLinkContainer.appendChild(userSettingsLink);

    // Add the user settings link at the top
    if (drawerContainer.firstChild) {
        drawerContainer.insertBefore(userSettingsLinkContainer, drawerContainer.firstChild);
    } else {
        drawerContainer.appendChild(userSettingsLinkContainer);
    }

    // Find or create a specific container for dynamic links
    let dynamicLinksContainer = drawerContainer.querySelector("#dynamic-drawer-links");
    if (!dynamicLinksContainer) {
        console.log("Creating #dynamic-drawer-links container inside .drawer");
        dynamicLinksContainer = document.createElement('div');
        dynamicLinksContainer.id = 'dynamic-drawer-links';
        // Prepend or append based on where you want the dynamic links relative to static ones
        drawerContainer.appendChild(dynamicLinksContainer); // Append at the end
    }

    console.log("Fetching dynamic drawer links...");
    // Show loading message inside the specific container
    dynamicLinksContainer.innerHTML = '<p style="padding: 15px; color: #aaa;">Loading links...</p>';

    try {
        const { data: links, error } = await supabaseClient
            .from(DRAWER_LINKS_TABLE)
            .select('text, url, icon_class')
            .eq('is_active', true)
            .order('order', { ascending: true });

        if (error) {
             // Check for RLS errors specifically
             if (error.message.includes("permission denied")) {
                 console.error("Permission denied fetching drawer links. Check RLS policies for anon key on 'drawer_links' table.");
                 throw new Error("Permission denied fetching drawer links.");
             } else {
                throw error; // Re-throw other errors
             }
        }

        console.log("Fetched drawer links data:", links); // Log fetched data

        // Clear only the dynamic links container
        dynamicLinksContainer.innerHTML = '';

        if (links && links.length > 0) {
            links.forEach(link => {
                const linkElement = document.createElement('a');
                linkElement.href = link.url || '#';
                if (link.url && (link.url.startsWith('http') || link.url.startsWith('https'))) {
                    linkElement.target = '_blank'; // فتح اروابط اخارجية في نافذة جديدة
                    linkElement.rel = 'noopener noreferrer'; // أمان
                }

                let iconHtml = '';
                if (link.icon_class) {
                    // تحقق إذا كنت تستخدم FontAwesome أو صورًا أيقونات
                    // إذا FontAwesome:
                    // iconHtml = `<i class="${link.icon_class}" style="margin-right: 8px;"></i> `;
                    // إذا صور (عدّ حسب حاجتك):
                    // iconHtml = `<img src="${link.icon_class}" alt="" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;"> `;
                    // اكود من انسخة احاية يستخدم FontAwesome:
                    if (link.icon_class.startsWith('fa-') || link.icon_class.startsWith('fas ') || link.icon_class.startsWith('fab ') || link.icon_class.startsWith('far ')) {
                         iconHtml = `<i class="${link.icon_class}" style="margin-right: 8px; width: 20px; text-align: center;"></i> `;
                    } else {
                        // افترض أنه مسار صورة إذا م يكن كاس FontAwesome
                        iconHtml = `<img src="${link.icon_class}" alt="" style="width: 20px; height: 20px; margin-right: 8px; vertical-align: middle;"> `;
                    }
                }

                linkElement.innerHTML = `${iconHtml}${link.text || 'Link'}`;
                // أضف كاسات رابط إذا زم اأمر تنسيق
                // linkElement.className = 'drawer-link-item';
                linkElement.style.cssText = `
                    display: block; padding: 12px 15px; text-decoration: none;
                    color: #ddd; /* أو ون مناسب */ border-bottom: 1px solid #444;
                    transition: background-color 0.2s;
                `; // أنماط أساسية
                linkElement.onmouseover = () => linkElement.style.backgroundColor = '#444';
                linkElement.onmouseout = () => linkElement.style.backgroundColor = 'transparent';

                dynamicLinksContainer.appendChild(linkElement); // Use the correct container variable
            });
            console.log(`Successfully loaded ${links.length} drawer links.`);
        } else {
             // Display message inside the specific container
            dynamicLinksContainer.innerHTML = '<p style="padding: 15px; color: #aaa;">No links available.</p>';
        }

    } catch (error) {
        console.error("Error fetching or displaying drawer links:", error);
        // Check if it's a network-related error
        if (error.message && (error.message.includes('Failed to fetch') ||
                              error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                              error.message.includes('TypeError: Failed to fetch'))) {
            showNetworkError();
        }
         // Display error inside the specific container
        dynamicLinksContainer.innerHTML = `<p style="padding: 15px; color: #f87171;">Error loading links: ${error.message}</p>`;
    }
}

async function fetchAndDisplayAppAnnouncements() {
    console.log("Fetching active app announcements...");
    try {
        const { data, error } = await supabaseClient
            .from(APP_ANNOUNCEMENTS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching app announcements:', error);
            // Check if it's a network-related error
            if (error.message && (error.message.includes('Failed to fetch') ||
                                  error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                                  error.message.includes('TypeError: Failed to fetch'))) {
                showNetworkError();
            }
            return;
        }

        if (data && data.length > 0) {
            const userId = generateUserId();
            for (const announcement of data) {
                const announcementKey = `announcement_shown_${userId}_${announcement.id}`;
                if (announcement.display_frequency === 'once') {
                    if (localStorage.getItem(announcementKey)) {
                        console.log(`Announcement ${announcement.id} (once) already shown. Skipping.`);
                        continue;
                    }
                }
                createAnnouncementModal(announcement, userId);
                break; // عرض إعان واحد فقط ك تحمي صفحة مبدئيًا
            }
        } else {
            console.log('No active app announcements found.');
        }
    } catch (err) {
        console.error('Unexpected error in fetchAndDisplayAppAnnouncements:', err);
    }
}

function createAnnouncementModal(announcement, userId) {
    const modalId = `app-announcement-modal-${announcement.id}`;
    if (document.getElementById(modalId)) return; // تجنب اتكرار

    const modalOverlay = document.createElement('div');
    modalOverlay.id = modalId;
    // ... (انسخ بقية محتوى داة createAnnouncementModal من انسخة احاية - اأطو)
    // مرة أخرى، انتبه مسارات أيقونة اإغاق وأنماط CSS.
    // اأنماط ارئيسية:
    modalOverlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.75); display: flex;
        justify-content: center; align-items: center; z-index: 100006; /* أعى من إشعار اتحديث */
        padding: 15px; box-sizing: border-box;
    `;

    const card = document.createElement('div');
    card.className = 'announcement-card'; // ستحتاج تعريف هذا اكاس في CSS
    card.style.cssText = `
        background: #1e1e1e; color: #ffffff; padding: 20px; border-radius: 12px;
        border-image: linear-gradient(to right, #FFD700, #FFC300) 1; border-width: 2px; border-style: solid;
        box-shadow: 0 8px 25px rgba(0,0,0,0.6); width: 100%; max-width: 400px;
        display: flex; flex-direction: column; align-items: center; text-align: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* أو اخط اذي تستخدمه */
        position: relative; overflow: hidden;
        /* animation: fadeInScaleUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); */ /* تجاه ارسوم امتحركة */
    `;

    const closeButton = document.createElement('button');
    const closeIconImg2 = document.createElement('img');
    closeIconImg2.src = 'image/close_icon.png'; // تأكد من صحة امسار
    closeIconImg2.alt = 'Close';
    closeIconImg2.style.width = '20px'; closeIconImg2.style.height = '20px';
    closeButton.appendChild(closeIconImg2);
    closeButton.setAttribute('aria-label', 'إغاق اإعان');
    closeButton.style.cssText = `
        position: absolute; top: 10px; right: 10px; background: transparent;
        border: none; cursor: pointer; line-height: 1; padding: 0 5px; z-index: 10;
    `;
    closeButton.onclick = (e) => {
        e.stopPropagation(); modalOverlay.remove();
        if (announcement.display_frequency === 'once') {
            localStorage.setItem(`announcement_shown_${userId}_${announcement.id}`, 'true');
        }
        document.body.style.overflow = '';
    };
    card.appendChild(closeButton);

    const isImageOnlyAnnouncement = announcement.image_url && (!announcement.description || announcement.description.trim() === '');

    if (isImageOnlyAnnouncement) {
        const img = document.createElement('img');
        img.src = announcement.image_url;
        img.alt = announcement.title || 'Announcement Image';
        img.style.cssText = `
            width: 100%; max-height: 70vh; object-fit: contain;
            border-radius: 8px; margin-bottom: 20px;
            border: 3px solid; border-image-slice: 1;
            border-image-source: linear-gradient(to right, #FFD700, #FFA500); display: block;
        `;
        card.appendChild(img);

        if (announcement.title && announcement.title.trim() !== '') {
            const titleElement = document.createElement('h3');
            titleElement.textContent = announcement.title;
            titleElement.style.cssText = `color: #ffcc00; font-size: 1.5em; margin-bottom: 20px; font-weight: 600;`;
            card.appendChild(titleElement);
        }

        const continueButton = document.createElement('button');
        continueButton.textContent = 'Continue';
        continueButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
            padding: 12px 25px; border-radius: 8px; cursor: pointer; font-size: 1.1em;
            font-weight: bold; text-transform: uppercase; width: auto; min-width: 150px;
            font-family: 'Courier New', Courier, monospace; margin-top: 10px;
            /* box-shadow: 0 2px 5px rgba(0,0,0,0.3); */ /* تجاه اظ */
        `;
        continueButton.onclick = (e) => {
            e.stopPropagation(); modalOverlay.remove();
            if (announcement.display_frequency === 'once') {
                localStorage.setItem(`announcement_shown_${userId}_${announcement.id}`, 'true');
            }
            document.body.style.overflow = '';
        };
        card.appendChild(continueButton);

    } else { // إعان عادي
        if (announcement.image_url) {
            const img = document.createElement('img');
            img.src = announcement.image_url;
            img.alt = announcement.title || 'Announcement Image';
            img.style.cssText = `
                width: 100%; max-height: 200px; object-fit: cover;
                border-radius: 8px; margin-bottom: 15px; border: 1px solid #444;
            `;
            card.appendChild(img);
        }
        if (announcement.title && announcement.title.trim() !== '') {
            const titleElement = document.createElement('h3');
            titleElement.textContent = announcement.title;
            titleElement.style.cssText = `color: #ffcc00; font-size: 1.5em; margin-bottom: 10px; font-weight: 600;`;
            card.appendChild(titleElement);
        }
        if (announcement.description && announcement.description.trim() !== '') {
            const descriptionElement = document.createElement('p');
            descriptionElement.textContent = announcement.description;
            descriptionElement.style.cssText = `
                font-size: 0.95em; line-height: 1.5; margin-bottom: 20px; color: #e0e0e0;
                max-height: 100px; overflow-y: auto;
            `;
            card.appendChild(descriptionElement);
        }
        if (announcement.button_text) {
            const actionButton = document.createElement('button');
            actionButton.textContent = announcement.button_text;
            actionButton.style.cssText = `
                background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
                padding: 12px 25px; border-radius: 8px; cursor: pointer; font-size: 1.1em;
                font-weight: bold; text-transform: uppercase; width: auto; min-width: 150px;
                font-family: 'Courier New', Courier, monospace;
                /* box-shadow: 0 2px 5px rgba(0,0,0,0.3); */ /* تجاه اظ */
            `;
            actionButton.onclick = (e) => {
                e.stopPropagation(); modalOverlay.remove();
                if (announcement.display_frequency === 'once') {
                    localStorage.setItem(`announcement_shown_${userId}_${announcement.id}`, 'true');
                }
                document.body.style.overflow = '';
            };
            card.appendChild(actionButton);
        }
    }
    modalOverlay.appendChild(card);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden';
}

async function fetchAndDisplayUpdateNotification() {
    console.log("Fetching active update notifications (new logic)...");
    try {
        // 1. Fetch all active notifications, ordered by priority (desc) then created_at (desc)
        const { data: notifications, error } = await supabaseClient
            .from(UPDATE_NOTIFICATIONS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('priority', { ascending: false }) // Higher priority first
            .order('created_at', { ascending: false }); // Then newest first for same priority

        if (error) {
            console.error('Error fetching update notifications:', error);
            return;
        }

        if (!notifications || notifications.length === 0) {
            console.log('[UpdateCheckV2] No active update notifications found.');
            return;
        }

        // 2. Get current app version
        let currentAppVersion = null;
        if (typeof AndroidInterface !== 'undefined' && typeof AndroidInterface.getAppVersionName === 'function') {
            try {
                currentAppVersion = AndroidInterface.getAppVersionName();
            } catch (e) {
                console.error("Error calling AndroidInterface.getAppVersionName():", e);
            }
        } else {
            console.warn('AndroidInterface.getAppVersionName() is not defined.');
        }

        if (!currentAppVersion || typeof currentAppVersion !== 'string' || currentAppVersion.trim() === "") {
            console.warn('[UpdateCheckV2] Current app version is invalid or not available. Cannot process version-specific notifications accurately.');
            // Fallback: Display the highest priority non-version-specific notification if any, or the first one.
            // Additional safety check for notifications array
            if (!notifications || !Array.isArray(notifications)) {
                console.error('[UpdateCheckV2] Notifications is not a valid array');
                return;
            }

            const allVersionsNotification = notifications.find(n => n && n.version_targeting_type === 'ALL_VERSIONS');
            if (allVersionsNotification) {
                 if (!sessionStorage.getItem(`notification_closed_${allVersionsNotification.id}`)) {
                    console.log(`[UpdateCheckV2] App version unknown, showing 'ALL_VERSIONS' notification ID ${allVersionsNotification.id}`);
                    createNotificationBanner(allVersionsNotification);
                 } else {
                    console.log(`[UpdateCheckV2] App version unknown, 'ALL_VERSIONS' notification ID ${allVersionsNotification.id} already closed.`);
                 }
            } else if (notifications.length > 0 && notifications[0] && !sessionStorage.getItem(`notification_closed_${notifications[0].id}`)) {
                // Fallback to the very first notification if no ALL_VERSIONS and app version is unknown
                console.warn(`[UpdateCheckV2] App version unknown, no 'ALL_VERSIONS' notification. Showing highest priority notification ID ${notifications[0].id} as fallback.`);
                createNotificationBanner(notifications[0]);
            }
            return;
        }
        console.log(`[UpdateCheckV2] Current App Version: '${currentAppVersion}'`);

        // 3. Iterate through notifications by priority
        for (const notification of notifications) {
            if (sessionStorage.getItem(`notification_closed_${notification.id}`)) {
                console.log(`[UpdateCheckV2] Notification ${notification.id} was already closed in this session. Skipping.`);
                continue;
            }

            const targetVersion = notification.version_name; // e.g., "1.1.0"
            const targetingType = notification.version_targeting_type || 'OLDER_THAN_TARGET'; // Default if null
            const isExclusive = notification.is_exclusive || false;

            console.log(`[UpdateCheckV2] Processing Notif ID ${notification.id}: TargetVer='${targetVersion}', Type='${targetingType}', Exclusive=${isExclusive}, Priority=${notification.priority}`);

            let matches = false;

            switch (targetingType) {
                case 'OLDER_THAN_TARGET':
                    // Show if currentAppVersion < targetVersion
                    // Requires a valid targetVersion
                    if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) < 0;
                    } else {
                        console.warn(`[UpdateCheckV2] Notif ID ${notification.id}: OLDER_THAN_TARGET requires a valid version_name. Skipping.`);
                        matches = false; // Cannot apply if targetVersion is invalid
                    }
                    break;
                case 'EXACTLY_TARGET':
                    // Show if currentAppVersion == targetVersion
                    // Requires a valid targetVersion
                     if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) === 0;
                    } else {
                        console.warn(`[UpdateCheckV2] Notif ID ${notification.id}: EXACTLY_TARGET requires a valid version_name. Skipping.`);
                        matches = false;
                    }
                    break;
                case 'ALL_BUT_TARGET':
                    // Show if currentAppVersion != targetVersion
                    // Requires a valid targetVersion
                    if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) !== 0;
                    } else {
                        // If no target version, it means "all but nothing", so effectively "all versions"
                        console.warn(`[UpdateCheckV2] Notif ID ${notification.id}: ALL_BUT_TARGET with no valid version_name. Treating as ALL_VERSIONS.`);
                        matches = true;
                    }
                    break;
                case 'ALL_VERSIONS':
                    // Always show
                    matches = true;
                    break;
                default:
                    console.warn(`[UpdateCheckV2] Unknown version_targeting_type: '${targetingType}' for Notif ID ${notification.id}. Defaulting to not match.`);
                    matches = false;
                    break;
            }

            console.log(`[UpdateCheckV2] Notif ID ${notification.id}: Match result for type '${targetingType}' with current '${currentAppVersion}' and target '${targetVersion}': ${matches}`);

            if (matches) {
                console.log(`[UpdateCheckV2] SHOWING notification ID ${notification.id}.`);
                createNotificationBanner(notification);
                if (isExclusive) {
                    console.log(`[UpdateCheckV2] Notification ID ${notification.id} is exclusive. Stopping further processing.`);
                    return; // Stop processing if exclusive
                }
                // If not exclusive, we might still want to show only one banner at a time.
                // For now, the logic will display the first matching, and if it's exclusive, it stops.
                // If not exclusive, it would continue, but createNotificationBanner might replace previous.
                // To show only one banner (the highest priority matching one), we should return after the first createNotificationBanner.
                return; // Display only the highest-priority matching notification
            }
        }
        console.log('[UpdateCheckV2] No suitable update notification found after checking all active ones.');

    } catch (err) {
        console.error('[UpdateCheckV2] Unexpected error in fetchAndDisplayUpdateNotification:', err);
    }
}

// Helper function to compare semantic versions (e.g., "1.2.3", "1.10.0")
// Returns:
//   -1 if v1 < v2
//    0 if v1 == v2
//    1 if v1 > v2
function compareVersions(v1, v2) {
    if (!v1 || !v2) return 0; // Handle null/undefined

    // Ensure inputs are strings before splitting
    const sV1 = String(v1);
    const sV2 = String(v2);

    const parts1 = sV1.split('.').map(Number);
    const parts2 = sV2.split('.').map(Number); // Fixed: was using v2 instead of sV2
    const len = Math.max(parts1.length, parts2.length);

    for (let i = 0; i < len; i++) {
        const p1 = parts1[i] || 0;
        const p2 = parts2[i] || 0;
        if (p1 < p2) return -1;
        if (p1 > p2) return 1;
    }
    return 0;
}


function createNotificationBanner(notification) {
    const modalId = `update-notification-modal-${notification.id}`;
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = modalId;
    // ... (انسخ بقية محتوى داة createNotificationBanner من انسخة احاية - اأطو)
    // تأكد من تعدي مسارات اصور أيقونات (مث أيقونة اإغاق) تناسب انسخة اسابقة
    // مثا تعدي زر اإغاق إذا كنت تستخدم صورة في انسخة اسابقة:
    // const closeButton = document.createElement('button');
    // const closeIconImg = document.createElement('img');
    // closeIconImg.src = 'image/close_icon.png'; // أو امسار اصحيح في انسخة اسابقة
    // closeIconImg.alt = 'Close';
    // closeIconImg.style.width = '20px'; closeIconImg.style.height = '20px';
    // closeButton.appendChild(closeIconImg);
    // ... (باقي خصائص closeButton)

    // انسخ اـ CSS اازم من انسخة احاية أو أنشئه ـ .notification-card وغيرها.
    // أهم اأنماط اتي يجب نسخها:
    modalOverlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.6); display: flex;
        justify-content: center; align-items: center; z-index: 100005; /* تأكد أن z-index مناسب */
        padding: 20px; box-sizing: border-box;
    `;

    const card = document.createElement('div');
    card.className = 'notification-card'; // ستحتاج تعريف هذا اكاس في CSS
    card.style.cssText = `
        background: linear-gradient(145deg, #2c2c2c, #1a1a1a); color: #ffffff;
        padding: 25px; border-radius: 15px;
        border-image: linear-gradient(to right, #FFD700, #FFC300) 1; border-width: 3px; border-style: solid;
        box-shadow: 0 10px 30px rgba(0,0,0,0.5); width: 100%; max-width: 380px;
        min-height: 300px; display: flex; flex-direction: column;
        justify-content: space-between; text-align: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* أو اخط اذي تستخدمه */
        position: relative; overflow: hidden;
        /* animation: fadeInScaleUp 0.3s ease-out; */ /* تجاه ارسوم امتحركة مبدئيًا */
    `;

    const closeButton = document.createElement('button');
    const closeIconImg1 = document.createElement('img');
    closeIconImg1.src = 'image/close_icon.png'; // تأكد من صحة امسار
    closeIconImg1.alt = 'Close';
    closeIconImg1.style.width = '20px'; closeIconImg1.style.height = '20px';
    closeButton.appendChild(closeIconImg1);
    closeButton.setAttribute('aria-label', 'إغاق اإشعار');
    closeButton.style.cssText = `
        position: absolute; top: 10px; right: 10px; background: transparent;
        border: none; cursor: pointer; line-height: 1; padding: 0 5px;
    `;
    closeButton.onclick = (e) => {
        e.stopPropagation(); modalOverlay.remove();
        sessionStorage.setItem(`notification_closed_${notification.id}`, 'true');
        document.body.style.overflow = '';
    };

    let imageElementHtml = '';
    if (notification.image_url) {
        const img = document.createElement('img');
        img.src = notification.image_url;
        img.alt = notification.title || 'Notification Image';
        img.style.cssText = `
            width: 100%; max-height: 180px; object-fit: cover;
            border-radius: 10px; margin-bottom: 15px;
            border-image: linear-gradient(to right, orange, gold) 1; border-width: 2px; border-style: solid;
        `;
        card.appendChild(img);
    }

    const titleElement = document.createElement('h3');
    titleElement.id = `notification-title-${notification.id}`;
    titleElement.textContent = notification.title || 'تحديث جديد متوفر!';
    titleElement.style.cssText = `
        color: #ffcc00; font-size: 1.6em;
        margin-top: ${notification.image_url ? '0' : '10px'}; margin-bottom: 15px; font-weight: bold;
    `;

    const descriptionElement = document.createElement('p');
    descriptionElement.textContent = notification.description || 'يتوفر إصدار جديد من اتطبيق. قم باتحديث اآن!';
    descriptionElement.style.cssText = `font-size: 1em; line-height: 1.6; margin-bottom: 25px; color: #e0e0e0;`;

    const actionsContainer = document.createElement('div');
    actionsContainer.style.cssText = `display: flex; flex-direction: column; gap: 10px; width: 100%;`;

    if (notification.update_url) {
        const updateButton = document.createElement('button');
        updateButton.textContent = 'Update Now';
        // انسخ أنماط ازر من انسخة احاية أو أنشئها. مثا بسيط:
        updateButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
            padding: 15px 20px; border-radius: 5px; cursor: pointer; font-size: 1.2em;
            font-weight: bold; text-transform: uppercase; width: 100%;
            font-family: 'Courier New', Courier, monospace; /* أو خط مناسب */
            /* box-shadow: 2px 2px 0px #a0522d; */ /* تجاه اظ مبدئيًا */
        `;
        updateButton.onclick = (e) => {
            e.stopPropagation();
            if (typeof AndroidInterface !== 'undefined' && AndroidInterface.openExternalUrl) {
                AndroidInterface.openExternalUrl(notification.update_url);
            } else {
                window.open(notification.update_url, '_blank');
            }
        };
        actionsContainer.appendChild(updateButton);
    }

    if (notification.show_cancel_button) {
        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Skip';
        // انسخ أنماط ازر من انسخة احاية أو أنشئها. مثا بسيط:
        cancelButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
            padding: 10px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;
            font-weight: bold; width: 100%;
            font-family: 'Courier New', Courier, monospace; /* أو خط مناسب */
        `;
        cancelButton.onclick = (e) => {
            e.stopPropagation(); modalOverlay.remove();
            sessionStorage.setItem(`notification_closed_${notification.id}`, 'true');
            document.body.style.overflow = '';
        };
        actionsContainer.appendChild(cancelButton);
    }

    card.appendChild(closeButton);
    card.appendChild(titleElement);
    card.appendChild(descriptionElement);
    card.appendChild(actionsContainer);
    modalOverlay.appendChild(card);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden'; // منع اتمرير في اخفية
}

// --- Lazy Loading ابسيط - انسخة اقديمة امحسنة ---
let lazyLoadObserver;

function initializeLazyLoading(selector = '.lazy-load') {
    const imagesToLoad = document.querySelectorAll(selector + ':not(.loaded)'); // Select only images not yet loaded

    if (!lazyLoadObserver) {
        lazyLoadObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.getAttribute('data-src');
                    if (src) {
                        img.src = src;
                        img.classList.add('loaded'); // Mark as loaded
                        img.classList.remove('lazy-load'); // Optional: remove class if no longer needed
                        console.log(`Lazy loading image: ${src.substring(0, 50)}...`);
                    }
                    observer.unobserve(img); // Stop observing once loaded
                }
            });
        }, {
            rootMargin: '0px 0px 100px 0px', // Load images 100px before they enter viewport
            threshold: 0.01 // Trigger even if only 1% visible
        });
    }

    imagesToLoad.forEach(img => {
        lazyLoadObserver.observe(img);
    });
}


// --- New Installation Instructions Modal ---
function showNewInstallInstructions() {
    console.log(">>> showNewInstallInstructions called");

    // Check if modal already exists
    if (document.getElementById('install-instructions-modal')) {
        console.log("Install instructions modal already exists.");
        return;
    }

    // Create modal container
    const modal = document.createElement('div');
    modal.id = 'install-instructions-modal';
    // Add classes or styles via CSS later

    // Create header div
    const header = document.createElement('div');
    header.className = 'modal-install-header';

    // Create close button inside header
    const closeButton = document.createElement('button');
    closeButton.className = 'close-button'; // Use class for styling
    closeButton.innerHTML = '&times;'; // '×' symbol
    closeButton.title = 'Close';
    closeButton.onclick = closeNewInstallInstructions; // Assign close function
    header.appendChild(closeButton);

    // (Optional: Add title to header if needed)
    // const title = document.createElement('h2');
    // title.textContent = 'How to Install';
    // header.appendChild(title);

    // Create image container div
    const imageContainer = document.createElement('div');
    imageContainer.id = 'install-image-container';

    // Add touch listeners to disable/enable pull-to-refresh
    imageContainer.addEventListener('touchstart', () => {
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.disablePullToRefresh) {
            console.log(">>> Disabling pull-to-refresh for instruction scroll");
            AndroidInterface.disablePullToRefresh();
        }
    }, { passive: true }); // Use passive listener if just observing

    imageContainer.addEventListener('touchend', () => {
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.enablePullToRefresh) {
            console.log(">>> Re-enabling pull-to-refresh after instruction scroll");
            AndroidInterface.enablePullToRefresh();
        }
    });
     imageContainer.addEventListener('touchcancel', () => { // Also re-enable on cancel
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.enablePullToRefresh) {
            console.log(">>> Re-enabling pull-to-refresh after touch cancel");
            AndroidInterface.enablePullToRefresh();
        }
    });


    // Loop to create and append images 1 to 6
    for (let i = 1; i <= 6; i++) {
        const img = document.createElement('img');
        img.src = `image/${i}.jpg`; // Use relative path
        img.alt = `Installation Instructions Step ${i}`;
        // Add click listener for zoom
        img.addEventListener('click', () => showImageZoomModal(img.src));
        // Add styling via CSS
        imageContainer.appendChild(img);
    }

    // Append header and image container to modal
    modal.appendChild(header);
    modal.appendChild(imageContainer);

    // Append modal to body
    document.body.appendChild(modal);

    // Add class to body to prevent scrolling (optional, if needed)
    // document.body.classList.add('modal-open'); // Keep body scrollable
}

function closeNewInstallInstructions() {
    console.log(">>> closeNewInstallInstructions called");
    // Also close zoom modal if open when closing instructions
    closeImageZoomModal();
    const modal = document.getElementById('install-instructions-modal');
    if (modal) {
        modal.remove(); // Remove the install instructions modal from the DOM
    }
    // Ensure pull-to-refresh is re-enabled when closing the modal
    if (typeof AndroidInterface !== 'undefined' && AndroidInterface.enablePullToRefresh) {
        console.log(">>> Ensuring pull-to-refresh is enabled after closing instructions");
        AndroidInterface.enablePullToRefresh();
    }
}

// --- Image Zoom Modal Functions ---
function showImageZoomModal(src) {
    console.log(">>> showImageZoomModal called with src:", src);
    // Close any existing zoom modal first
    closeImageZoomModal();

    // Create modal container
    const zoomModal = document.createElement('div');
    zoomModal.id = 'image-zoom-modal';
    zoomModal.onclick = closeImageZoomModal; // Close when background is clicked

    // Create image element
    const zoomImage = document.createElement('img');
    zoomImage.src = src;
    zoomImage.alt = 'Zoomed Installation Instruction';
    zoomImage.onclick = (event) => {
        event.stopPropagation(); // Prevent closing modal when clicking the image itself
    };

    // Append image to modal, and modal to body
    zoomModal.appendChild(zoomImage);
    document.body.appendChild(zoomModal);
    // Optional: Prevent body scroll while zoom modal is open
    // document.body.classList.add('modal-open');
}

function closeImageZoomModal() {
    const zoomModal = document.getElementById('image-zoom-modal');
    if (zoomModal) {
        console.log(">>> Closing image zoom modal");
        zoomModal.remove();
        // Optional: Restore body scroll if it was prevented
        // document.body.classList.remove('modal-open');
    }
}
// --- End Image Zoom Modal Functions ---




// --- Utility: Apply Fade-in Animation ---
function applyFadeInAnimation(selector) {
     const elements = document.querySelectorAll(selector);
     if (elements.length > 0) {
         setTimeout(() => {
            elements.forEach((el, index) => {
                 el.style.animationDelay = `${index * 0.05}s`;
                 el.classList.add('fade-in');
            });
        }, 50);
      }
 }



// --- Pull-to-Refresh Handler ---
async function handlePullToRefresh() {
    console.log(">>> handlePullToRefresh called from native.");

    // 1. Clear relevant cache
    // We need to clear the cache for the *current* view (All or specific category/sort)
    // For simplicity, let's clear all 'mods_' cache entries. A more targeted approach
    // could be implemented if performance becomes an issue.
    try {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('mods_')) {
                localStorage.removeItem(key);
                console.log(`Pull-to-refresh: Removed cache key: ${key}`);
            }
        }
    } catch (e) {
        console.error("Error clearing cache during pull-to-refresh:", e);
    }

    // 2. Re-fetch and re-display data based on the current view
    try {
        if (currentCategory === 'All') {
            // Re-display the "All" view (sections)
            await displayModsBySection();
        } else {
            // Re-display the current single category view with current sort order
            await displaySingleCategory(currentCategory, currentSortBy, currentSortAscending);
        }
    } catch (error) {
        console.error("Error re-fetching data during pull-to-refresh:", error);
        // Optionally display an error message in the UI
    } finally {
        // 3. Hide the native refresh indicator via the Android Interface
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.hideRefreshIndicator) {
            AndroidInterface.hideRefreshIndicator();
            console.log("Pull-to-refresh: Called AndroidInterface.hideRefreshIndicator()");
        } else {
            console.warn("Pull-to-refresh: AndroidInterface.hideRefreshIndicator not available.");
            // Fallback for testing in browser: hide a potential web-based indicator if you add one
        }
    }
}
// --- End Pull-to-Refresh Handler ---

// --- REMOVED: Simulate initial click ---
// function simulateInitialClick() { ... }
// --- END REMOVED ---

console.log("Mod Etaris script loaded and Supabase initialized.");

// --- Function definition moved higher up ---


// Ensure attachDelayedEventListeners is called when DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOMContentLoaded fired, calling attachDelayedEventListeners.");
    attachDelayedEventListeners();
});
