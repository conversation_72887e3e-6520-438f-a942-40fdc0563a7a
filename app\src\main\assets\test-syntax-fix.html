<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الخطأ النحوي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار إصلاح الخطأ النحوي</h1>
        <p>هذا الاختبار يتحقق من إصلاح الخطأ في السطر 1174 من script.js</p>
        
        <div id="test-results"></div>
        
        <button onclick="runSyntaxTest()">🧪 تشغيل اختبار البنية النحوية</button>
        <button onclick="testOptionalChaining()">⚡ اختبار Optional Chaining</button>
        <button onclick="testScriptLoading()">📜 اختبار تحميل script.js</button>
        <button onclick="clearResults()">🧹 مسح النتائج</button>
        
        <div id="console-output"></div>
    </div>

    <script>
        // تسجيل رسائل وحدة التحكم
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function logToOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#dc3545' : '#28a745';
            logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToOutput(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToOutput(args.join(' '), 'error');
        };

        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function runSyntaxTest() {
            console.log('🧪 بدء اختبار البنية النحوية...');
            
            try {
                // محاكاة الكود المُصلح
                const downloadButton = document.createElement('button');
                downloadButton.innerHTML = '<span class="download-btn-text">تحميل</span>';
                
                // هذا هو السطر المُصلح (1174)
                const downloadButtonText = downloadButton?.querySelector('.download-btn-text');
                
                if (downloadButtonText) {
                    addTestResult('✅ تم إصلاح الخطأ النحوي بنجاح! Optional Chaining يعمل بشكل صحيح', 'success');
                    console.log('✅ Optional Chaining يعمل بشكل صحيح');
                } else {
                    addTestResult('⚠️ Optional Chaining يعمل لكن العنصر غير موجود', 'info');
                    console.log('⚠️ العنصر غير موجود لكن لا توجد أخطاء نحوية');
                }
                
            } catch (error) {
                addTestResult(`❌ خطأ في البنية النحوية: ${error.message}`, 'error');
                console.error('❌ خطأ في البنية النحوية:', error.message);
            }
        }

        function testOptionalChaining() {
            console.log('⚡ اختبار Optional Chaining المتقدم...');
            
            try {
                // اختبار مع عنصر موجود
                const existingElement = document.createElement('div');
                existingElement.innerHTML = '<span class="test-class">نص تجريبي</span>';
                const foundElement = existingElement?.querySelector('.test-class');
                
                if (foundElement) {
                    addTestResult('✅ Optional Chaining مع عنصر موجود: يعمل بشكل مثالي', 'success');
                    console.log('✅ اختبار العنصر الموجود نجح');
                }
                
                // اختبار مع عنصر غير موجود
                const nullElement = null;
                const notFoundElement = nullElement?.querySelector('.non-existent');
                
                if (notFoundElement === undefined) {
                    addTestResult('✅ Optional Chaining مع عنصر غير موجود: يعمل بشكل مثالي', 'success');
                    console.log('✅ اختبار العنصر غير الموجود نجح');
                }
                
                addTestResult('🎉 جميع اختبارات Optional Chaining نجحت!', 'success');
                console.log('🎉 جميع الاختبارات نجحت!');
                
            } catch (error) {
                addTestResult(`❌ خطأ في اختبار Optional Chaining: ${error.message}`, 'error');
                console.error('❌ خطأ في الاختبار:', error.message);
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-output').innerHTML = '';
            console.log('🧹 تم مسح النتائج');
        }

        // اختبار تحميل script.js
        function testScriptLoading() {
            console.log('📜 اختبار تحميل script.js...');
            
            const script = document.createElement('script');
            script.src = 'script.js';
            script.onload = function() {
                addTestResult('✅ تم تحميل script.js بنجاح - لا توجد أخطاء نحوية!', 'success');
                console.log('✅ script.js تم تحميله بدون أخطاء');
            };
            script.onerror = function() {
                addTestResult('❌ فشل في تحميل script.js - قد توجد أخطاء نحوية', 'error');
                console.error('❌ خطأ في تحميل script.js');
            };
            
            // إضافة معالج للأخطاء النحوية
            window.addEventListener('error', function(e) {
                if (e.filename && e.filename.includes('script.js')) {
                    addTestResult(`❌ خطأ نحوي في script.js السطر ${e.lineno}: ${e.message}`, 'error');
                    console.error(`❌ خطأ نحوي: السطر ${e.lineno} - ${e.message}`);
                }
            });
            
            document.head.appendChild(script);
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('📱 تم تحميل صفحة الاختبار');
            addTestResult('📱 صفحة الاختبار جاهزة - اضغط على الأزرار لتشغيل الاختبارات', 'info');
            
            // اختبار تحميل script.js تلقائياً
            setTimeout(testScriptLoading, 1000);
        });
    </script>
</body>
</html>
