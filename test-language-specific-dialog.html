<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المربع المنبثق حسب اللغة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .status {
            background: rgba(0, 255, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        .info {
            background: rgba(0, 123, 255, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid rgba(0, 123, 255, 0.3);
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار المربع المنبثق حسب اللغة</h1>
        <p>هذه الصفحة لاختبار المربع المنبثق الجديد الذي يظهر بلغة المستخدم المحددة فقط</p>

        <div class="test-section">
            <h2>📊 حالة اللغة الحالية</h2>
            <div class="status" id="currentLanguageStatus">
                جاري فحص اللغة الحالية...
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 تغيير اللغة للاختبار</h2>
            <button class="button" onclick="setLanguage('ar')">تعيين اللغة العربية</button>
            <button class="button" onclick="setLanguage('en')">Set English Language</button>
            <button class="button" onclick="clearLanguage()">مسح اللغة (افتراضي)</button>
        </div>

        <div class="test-section">
            <h2>🎯 اختبار المربع المنبثق</h2>
            <div class="info">
                <strong>ملاحظة:</strong> هذا الاختبار يحاكي استدعاء دالة تحميل المود. 
                في التطبيق الفعلي، سيظهر المربع المنبثق الأصلي من Android.
            </div>
            <button class="button" onclick="testDownloadDialog()">اختبار تحميل مود</button>
            <button class="button" onclick="resetDialogPreference()">إعادة تعيين تفضيلات المربع</button>
        </div>

        <div class="test-section">
            <h2>📝 سجل الاختبار</h2>
            <div class="code" id="testLog">
جاري تحميل سجل الاختبار...
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 معلومات تقنية</h2>
            <div class="info" id="technicalInfo">
                جاري تحميل المعلومات التقنية...
            </div>
        </div>
    </div>

    <script>
        // تحديث حالة اللغة
        function updateLanguageStatus() {
            const currentLang = localStorage.getItem('selectedLanguage') || 'غير محدد (افتراضي: en)';
            const statusElement = document.getElementById('currentLanguageStatus');
            statusElement.innerHTML = `
                <strong>اللغة الحالية:</strong> ${currentLang}<br>
                <strong>اتجاه النص:</strong> ${document.dir}<br>
                <strong>لغة المتصفح:</strong> ${navigator.language}
            `;
        }

        // تعيين اللغة
        function setLanguage(lang) {
            localStorage.setItem('selectedLanguage', lang);
            logMessage(`تم تعيين اللغة إلى: ${lang}`);
            updateLanguageStatus();
            updateTechnicalInfo();
            
            // تحديث اتجاه الصفحة
            if (lang === 'ar') {
                document.dir = 'rtl';
                document.lang = 'ar';
            } else {
                document.dir = 'ltr';
                document.lang = 'en';
            }
        }

        // مسح اللغة
        function clearLanguage() {
            localStorage.removeItem('selectedLanguage');
            logMessage('تم مسح إعداد اللغة - سيتم استخدام الافتراضي (en)');
            updateLanguageStatus();
            updateTechnicalInfo();
        }

        // اختبار مربع التحميل
        function testDownloadDialog() {
            const currentLang = localStorage.getItem('selectedLanguage') || 'en';
            const isArabic = currentLang === 'ar';
            
            logMessage(`محاولة عرض مربع التحميل باللغة: ${currentLang}`);
            
            // محاكاة المربع المنبثق
            let title, message, checkboxText, okText, cancelText;
            
            if (isArabic) {
                title = 'تحميل المود';
                message = 'لمشاهدة إعلان واحد لتحميل هذا المود، يرجى الضغط على موافق.';
                checkboxText = 'موافق دائماً (عدم الإظهار مرة أخرى)';
                okText = 'موافق';
                cancelText = 'إلغاء';
            } else {
                title = 'Download Mod';
                message = 'To download this mod, please watch one ad.';
                checkboxText = 'Always agree (Don\'t show again)';
                okText = 'OK';
                cancelText = 'Cancel';
            }
            
            // عرض المربع المحاكي
            const result = confirm(`${title}\n\n${message}\n\n[${checkboxText}]\n\n${okText} / ${cancelText}`);
            
            if (result) {
                logMessage(`المستخدم اختار: ${okText}`);
                logMessage('سيتم عرض الإعلان المكافئ الآن...');
            } else {
                logMessage(`المستخدم اختار: ${cancelText}`);
                logMessage('تم إلغاء عملية التحميل');
            }
        }

        // إعادة تعيين تفضيلات المربع
        function resetDialogPreference() {
            // في التطبيق الفعلي، هذا سيكون في SharedPreferences
            localStorage.removeItem('skip_ad_confirm_dialog');
            logMessage('تم إعادة تعيين تفضيلات المربع - سيظهر المربع في المرة القادمة');
        }

        // تسجيل رسالة
        function logMessage(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const currentLog = logElement.textContent;
            logElement.textContent = `[${timestamp}] ${message}\n${currentLog}`;
        }

        // تحديث المعلومات التقنية
        function updateTechnicalInfo() {
            const techInfo = document.getElementById('technicalInfo');
            const selectedLang = localStorage.getItem('selectedLanguage');
            const languageSelected = localStorage.getItem('languageSelected');
            
            techInfo.innerHTML = `
                <strong>localStorage.selectedLanguage:</strong> ${selectedLang || 'null'}<br>
                <strong>localStorage.languageSelected:</strong> ${languageSelected || 'null'}<br>
                <strong>navigator.language:</strong> ${navigator.language}<br>
                <strong>document.dir:</strong> ${document.dir}<br>
                <strong>document.lang:</strong> ${document.lang}<br>
                <strong>User Agent:</strong> ${navigator.userAgent.substring(0, 100)}...
            `;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageStatus();
            updateTechnicalInfo();
            logMessage('تم تحميل صفحة الاختبار بنجاح');
            logMessage('جاهز لاختبار المربع المنبثق حسب اللغة');
        });

        // تحديث دوري للمعلومات
        setInterval(() => {
            updateLanguageStatus();
            updateTechnicalInfo();
        }, 5000);
    </script>
</body>
</html>
